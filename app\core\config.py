"""
配置管理模块
提供统一的配置管理功能，支持从文件、环境变量和数据库读取配置
"""
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class OllamaConfig:
    """Ollama服务配置"""
    host: str = "http://localhost:11434"
    model_name: str = "qwen3:4b"
    timeout: int = 180
    max_retries: int = 3
    retry_delay: float = 1.0
    temperature: float = 0.1


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "data/documents.db"
    backup_enabled: bool = True
    backup_interval: int = 24  # hours
    max_backups: int = 7


@dataclass
class ParsingConfig:
    """文档解析配置"""
    supported_formats: list = None
    max_file_size_mb: int = 100
    ocr_enabled: bool = True
    extract_images: bool = True
    max_text_length: int = 50000
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.pdf', '.docx', '.txt', '.epub']


@dataclass
class UIConfig:
    """用户界面配置"""
    theme_mode: str = "light"
    window_width: int = 1200
    window_height: int = 900
    font_family: str = "Segoe UI, Microsoft YaHei, Arial, sans-serif"
    auto_save_settings: bool = True


@dataclass
class ProcessingConfig:
    """处理配置"""
    max_concurrent_files: int = 3
    batch_size: int = 10
    enable_duplicate_detection: bool = True
    auto_backup_before_analysis: bool = True


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.ollama = OllamaConfig()
        self.database = DatabaseConfig()
        self.parsing = ParsingConfig()
        self.ui = UIConfig()
        self.processing = ProcessingConfig()
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """从配置文件加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新各个配置对象
                if 'ollama' in config_data:
                    self._update_dataclass(self.ollama, config_data['ollama'])
                if 'database' in config_data:
                    self._update_dataclass(self.database, config_data['database'])
                if 'parsing' in config_data:
                    self._update_dataclass(self.parsing, config_data['parsing'])
                if 'ui' in config_data:
                    self._update_dataclass(self.ui, config_data['ui'])
                if 'processing' in config_data:
                    self._update_dataclass(self.processing, config_data['processing'])
                    
                self.logger.info(f"配置已从 {self.config_file} 加载")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.logger.info("使用默认配置")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 确保配置目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            config_data = {
                'ollama': asdict(self.ollama),
                'database': asdict(self.database),
                'parsing': asdict(self.parsing),
                'ui': asdict(self.ui),
                'processing': asdict(self.processing)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
                
            self.logger.info(f"配置已保存到 {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def _update_dataclass(self, obj, data: Dict[str, Any]):
        """更新dataclass对象的字段"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def get_env_override(self, key: str, default: Any = None) -> Any:
        """从环境变量获取配置覆盖"""
        env_key = f"WENYUANGE_{key.upper()}"
        return os.getenv(env_key, default)
    
    def update_ollama_config(self, **kwargs):
        """更新Ollama配置"""
        for key, value in kwargs.items():
            if hasattr(self.ollama, key):
                setattr(self.ollama, key, value)
        if self.ui.auto_save_settings:
            self.save_config()
    
    def update_ui_config(self, **kwargs):
        """更新UI配置"""
        for key, value in kwargs.items():
            if hasattr(self.ui, key):
                setattr(self.ui, key, value)
        if self.ui.auto_save_settings:
            self.save_config()


# 全局配置实例
config = ConfigManager()
