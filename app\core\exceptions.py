"""
自定义异常类和异常处理工具
"""
import logging
import traceback
from typing import Optional, Callable, Any
from functools import wraps


class WenyuangeException(Exception):
    """文渊阁应用基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.details = details or {}
    
    def __str__(self):
        return f"[{self.error_code}] {self.message}"


class DocumentParsingError(WenyuangeException):
    """文档解析异常"""
    
    def __init__(self, file_path: str, message: str, details: dict = None):
        super().__init__(
            message=f"解析文档失败: {file_path} - {message}",
            error_code="DOC_PARSING_ERROR",
            details={"file_path": file_path, **(details or {})}
        )
        self.file_path = file_path


class AIServiceError(WenyuangeException):
    """AI服务异常"""
    
    def __init__(self, service_name: str, message: str, details: dict = None):
        super().__init__(
            message=f"AI服务错误 ({service_name}): {message}",
            error_code="AI_SERVICE_ERROR",
            details={"service_name": service_name, **(details or {})}
        )
        self.service_name = service_name


class DatabaseError(WenyuangeException):
    """数据库异常"""
    
    def __init__(self, operation: str, message: str, details: dict = None):
        super().__init__(
            message=f"数据库操作失败 ({operation}): {message}",
            error_code="DATABASE_ERROR",
            details={"operation": operation, **(details or {})}
        )
        self.operation = operation


class ConfigurationError(WenyuangeException):
    """配置异常"""
    
    def __init__(self, config_key: str, message: str, details: dict = None):
        super().__init__(
            message=f"配置错误 ({config_key}): {message}",
            error_code="CONFIG_ERROR",
            details={"config_key": config_key, **(details or {})}
        )
        self.config_key = config_key


class FileOperationError(WenyuangeException):
    """文件操作异常"""
    
    def __init__(self, file_path: str, operation: str, message: str, details: dict = None):
        super().__init__(
            message=f"文件操作失败 ({operation}): {file_path} - {message}",
            error_code="FILE_OPERATION_ERROR",
            details={"file_path": file_path, "operation": operation, **(details or {})}
        )
        self.file_path = file_path
        self.operation = operation


class ValidationError(WenyuangeException):
    """数据验证异常"""
    
    def __init__(self, field: str, value: Any, message: str, details: dict = None):
        super().__init__(
            message=f"数据验证失败 ({field}): {message}",
            error_code="VALIDATION_ERROR",
            details={"field": field, "value": str(value), **(details or {})}
        )
        self.field = field
        self.value = value


def handle_exceptions(logger: logging.Logger = None, 
                     reraise: bool = False,
                     default_return: Any = None):
    """异常处理装饰器"""
    
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except WenyuangeException as e:
                if logger:
                    logger.error(f"业务异常: {e}")
                    if e.details:
                        logger.debug(f"异常详情: {e.details}")
                if reraise:
                    raise
                return default_return
            except Exception as e:
                if logger:
                    logger.error(f"未处理异常: {e}")
                    logger.debug(f"异常堆栈: {traceback.format_exc()}")
                if reraise:
                    raise
                return default_return
        return wrapper
    return decorator


def safe_execute(func: Callable, 
                logger: logging.Logger = None,
                error_message: str = "操作失败",
                default_return: Any = None) -> Any:
    """安全执行函数，捕获并记录异常"""
    try:
        return func()
    except WenyuangeException as e:
        if logger:
            logger.error(f"{error_message}: {e}")
        return default_return
    except Exception as e:
        if logger:
            logger.error(f"{error_message}: {e}")
            logger.debug(f"异常堆栈: {traceback.format_exc()}")
        return default_return


class ExceptionCollector:
    """异常收集器，用于批量处理时收集异常"""
    
    def __init__(self, logger: logging.Logger = None):
        self.exceptions = []
        self.logger = logger
    
    def add_exception(self, exception: Exception, context: str = ""):
        """添加异常"""
        self.exceptions.append({
            'exception': exception,
            'context': context,
            'timestamp': traceback.format_exc()
        })
        
        if self.logger:
            self.logger.error(f"收集到异常 ({context}): {exception}")
    
    def has_exceptions(self) -> bool:
        """是否有异常"""
        return len(self.exceptions) > 0
    
    def get_summary(self) -> str:
        """获取异常摘要"""
        if not self.exceptions:
            return "无异常"
        
        summary = f"共收集到 {len(self.exceptions)} 个异常:\n"
        for i, exc_info in enumerate(self.exceptions, 1):
            summary += f"{i}. [{exc_info['context']}] {exc_info['exception']}\n"
        
        return summary
    
    def clear(self):
        """清除异常记录"""
        self.exceptions.clear()


def create_error_handler(logger: logging.Logger):
    """创建错误处理器"""
    
    def handle_error(error: Exception, context: str = "", user_message: str = None):
        """处理错误"""
        if isinstance(error, WenyuangeException):
            logger.error(f"业务错误 ({context}): {error}")
            return user_message or error.message
        else:
            logger.error(f"系统错误 ({context}): {error}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
            return user_message or "系统发生未知错误，请查看日志获取详细信息"
    
    return handle_error
