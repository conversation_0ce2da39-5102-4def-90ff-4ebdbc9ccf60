"""
设置对话框组件
提供用户配置界面
"""
import flet as ft
from app.core.config import config
from app.core.logger import get_logger


class SettingsDialog:
    """设置对话框"""
    
    def __init__(self, page: ft.Page, on_settings_changed=None):
        self.page = page
        self.logger = get_logger(__name__)
        self.on_settings_changed = on_settings_changed
        
        # 创建控件
        self._create_controls()
        
        # 创建对话框
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text("设置", size=18, weight=ft.FontWeight.BOLD),
            content=self._create_content(),
            actions=[
                ft.TextButton("取消", on_click=self._on_cancel),
                ft.TextButton("保存", on_click=self._on_save),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def _create_controls(self):
        """创建控件"""
        # Ollama设置
        self.ollama_host = ft.TextField(
            label="Ollama服务地址",
            value=config.ollama.host,
            width=300
        )
        
        self.ollama_model = ft.TextField(
            label="模型名称",
            value=config.ollama.model_name,
            width=300
        )
        
        self.ollama_timeout = ft.TextField(
            label="超时时间(秒)",
            value=str(config.ollama.timeout),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        self.ollama_temperature = ft.TextField(
            label="温度参数",
            value=str(config.ollama.temperature),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        # 解析设置
        self.max_file_size = ft.TextField(
            label="最大文件大小(MB)",
            value=str(config.parsing.max_file_size_mb),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        self.ocr_enabled = ft.Checkbox(
            label="启用OCR",
            value=config.parsing.ocr_enabled
        )
        
        self.extract_images = ft.Checkbox(
            label="提取图片",
            value=config.parsing.extract_images
        )
        
        # UI设置
        self.theme_mode = ft.Dropdown(
            label="主题模式",
            width=200,
            options=[
                ft.dropdown.Option("light", "浅色"),
                ft.dropdown.Option("dark", "深色"),
                ft.dropdown.Option("system", "跟随系统")
            ],
            value=config.ui.theme_mode
        )
        
        self.window_width = ft.TextField(
            label="窗口宽度",
            value=str(config.ui.window_width),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        self.window_height = ft.TextField(
            label="窗口高度",
            value=str(config.ui.window_height),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        # 处理设置
        self.max_concurrent_files = ft.TextField(
            label="最大并发文件数",
            value=str(config.processing.max_concurrent_files),
            width=150,
            keyboard_type=ft.KeyboardType.NUMBER
        )
        
        self.enable_duplicate_detection = ft.Checkbox(
            label="启用重复检测",
            value=config.processing.enable_duplicate_detection
        )
        
        self.auto_backup = ft.Checkbox(
            label="分析前自动备份",
            value=config.processing.auto_backup_before_analysis
        )
    
    def _create_content(self):
        """创建对话框内容"""
        return ft.Container(
            content=ft.Column([
                # Ollama设置
                ft.ExpansionTile(
                    title=ft.Text("AI服务设置"),
                    subtitle=ft.Text("配置Ollama服务参数"),
                    controls=[
                        ft.Container(
                            content=ft.Column([
                                self.ollama_host,
                                self.ollama_model,
                                ft.Row([
                                    self.ollama_timeout,
                                    self.ollama_temperature
                                ])
                            ]),
                            padding=ft.padding.all(10)
                        )
                    ]
                ),
                
                # 解析设置
                ft.ExpansionTile(
                    title=ft.Text("文档解析设置"),
                    subtitle=ft.Text("配置文档解析参数"),
                    controls=[
                        ft.Container(
                            content=ft.Column([
                                self.max_file_size,
                                self.ocr_enabled,
                                self.extract_images
                            ]),
                            padding=ft.padding.all(10)
                        )
                    ]
                ),
                
                # UI设置
                ft.ExpansionTile(
                    title=ft.Text("界面设置"),
                    subtitle=ft.Text("配置用户界面参数"),
                    controls=[
                        ft.Container(
                            content=ft.Column([
                                self.theme_mode,
                                ft.Row([
                                    self.window_width,
                                    self.window_height
                                ])
                            ]),
                            padding=ft.padding.all(10)
                        )
                    ]
                ),
                
                # 处理设置
                ft.ExpansionTile(
                    title=ft.Text("处理设置"),
                    subtitle=ft.Text("配置文档处理参数"),
                    controls=[
                        ft.Container(
                            content=ft.Column([
                                self.max_concurrent_files,
                                self.enable_duplicate_detection,
                                self.auto_backup
                            ]),
                            padding=ft.padding.all(10)
                        )
                    ]
                )
            ], scroll=ft.ScrollMode.AUTO),
            width=500,
            height=600
        )
    
    def _on_cancel(self, e):
        """取消按钮点击"""
        self.dialog.open = False
        self.page.update()
    
    def _on_save(self, e):
        """保存按钮点击"""
        try:
            # 更新配置
            config.update_ollama_config(
                host=self.ollama_host.value,
                model_name=self.ollama_model.value,
                timeout=int(self.ollama_timeout.value),
                temperature=float(self.ollama_temperature.value)
            )
            
            config.parsing.max_file_size_mb = int(self.max_file_size.value)
            config.parsing.ocr_enabled = self.ocr_enabled.value
            config.parsing.extract_images = self.extract_images.value
            
            config.update_ui_config(
                theme_mode=self.theme_mode.value,
                window_width=int(self.window_width.value),
                window_height=int(self.window_height.value)
            )
            
            config.processing.max_concurrent_files = int(self.max_concurrent_files.value)
            config.processing.enable_duplicate_detection = self.enable_duplicate_detection.value
            config.processing.auto_backup_before_analysis = self.auto_backup.value
            
            # 保存配置
            config.save_config()
            
            # 关闭对话框
            self.dialog.open = False
            self.page.update()
            
            # 通知设置已更改
            if self.on_settings_changed:
                self.on_settings_changed()
                
            # 显示成功消息
            self._show_success_message()
            
        except Exception as ex:
            self.logger.error(f"保存设置失败: {ex}")
            self._show_error_message(f"保存设置失败: {ex}")
    
    def _show_success_message(self):
        """显示成功消息"""
        snack = ft.SnackBar(
            content=ft.Text("设置已保存"),
            bgcolor=ft.colors.GREEN
        )
        self.page.overlay.append(snack)
        snack.open = True
        self.page.update()
    
    def _show_error_message(self, message: str):
        """显示错误消息"""
        snack = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.colors.RED
        )
        self.page.overlay.append(snack)
        snack.open = True
        self.page.update()
    
    def show(self):
        """显示对话框"""
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
