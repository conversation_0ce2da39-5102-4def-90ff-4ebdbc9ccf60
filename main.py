import os
import sys
from pathlib import Path
import flet as ft
from app.ui.main_window import main_ft

# --- Environment Setup for PaddleOCR ---
# This MUST be done BEFORE any other application imports.
model_root_path = Path(__file__).resolve().parent / "models"
paddle_home = str(model_root_path).replace('\\', '/')
os.environ['PADDLE_HOME'] = paddle_home
model_root_path.mkdir(parents=True, exist_ok=True)

# 5. Debugging: Print the environment variables to confirm they are set
print("--- PaddleOCR Environment Setup ---")
print(f"PADDLE_HOME set to: {os.getenv('PADDLE_HOME')}")
print("-----------------------------------")


def main():
    """应用程序主入口"""
    # Flet 应用的启动方式
    ft.app(
        target=main_ft,
        assets_dir="assets"  # 指定资源目录
    )

if __name__ == "__main__":
    main() 