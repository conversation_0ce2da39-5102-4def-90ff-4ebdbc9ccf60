import os
import logging
import sys
import io
import fitz  # PyMuPDF
import docx
from PIL import Image
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import mimetypes

# --- Environment Setup for PaddleOCR is now in main.py ---

# Import PaddleOCR after the environment is configured in main.py
from paddleocr import PaddleOCR
from ebooklib import epub, ITEM_DOCUMENT, ITEM_IMAGE
from bs4 import BeautifulSoup

from app.core.config import config
from app.core.logger import get_logger, log_performance
from app.core.exceptions import DocumentParsingError, FileOperationError, handle_exceptions

# Suppress noisy logging from dependent libraries
logging.getLogger('ppocr').setLevel(logging.ERROR)
fitz.TOOLS.mupdf_display_errors(False)
fitz.TOOLS.mupdf_display_warnings(False)


class DocumentParser:
    """
    Handles parsing of different document formats, extracting text, images,
    and available metadata.
    支持PDF、DOCX、TXT、EPUB等格式，具备OCR功能
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.ocr_engine = None
        self.supported_formats = config.parsing.supported_formats
        self.max_file_size = config.parsing.max_file_size_mb * 1024 * 1024  # 转换为字节
        self.ocr_enabled = config.parsing.ocr_enabled
        self.extract_images = config.parsing.extract_images
        self.max_text_length = config.parsing.max_text_length

    def _validate_file(self, file_path: str) -> bool:
        """验证文件是否可以处理"""
        path = Path(file_path)

        # 检查文件是否存在
        if not path.exists():
            raise FileOperationError(file_path, "read", "文件不存在")

        # 检查文件大小
        file_size = path.stat().st_size
        if file_size > self.max_file_size:
            raise DocumentParsingError(
                file_path,
                f"文件过大 ({file_size / 1024 / 1024:.1f}MB > {self.max_file_size / 1024 / 1024}MB)"
            )

        # 检查文件格式
        ext = path.suffix.lower()
        if ext not in self.supported_formats:
            raise DocumentParsingError(file_path, f"不支持的文件格式: {ext}")

        return True

    @handle_exceptions(reraise=True)
    def _initialize_ocr(self):
        """Initializes the PaddleOCR engine as a singleton."""
        if not self.ocr_enabled:
            self.logger.info("OCR功能已禁用")
            return

        if self.ocr_engine is None:
            self.logger.info(f"PADDLE_HOME is set to: {os.environ.get('PADDLE_HOME')}")
            self.logger.info("正在初始化PaddleOCR引擎...")
            try:
                self.ocr_engine = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
                self.logger.info("PaddleOCR引擎初始化成功")
            except Exception as e:
                self.logger.error(f"OCR引擎初始化失败: {e}")
                self.ocr_engine = None
                raise DocumentParsingError("", f"OCR引擎初始化失败: {e}")

    @handle_exceptions(reraise=True)
    def _parse_pdf(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from a PDF file."""
        text, images_data, metadata = "", [], {}

        with log_performance(self.logger):
            try:
                with fitz.open(doc_path) as doc:
                    self.logger.debug(f"解析PDF文件: {doc_path}, 页数: {doc.page_count}")

                    # 提取文本
                    text_list = []
                    for page_num, page in enumerate(doc):
                        page_text = page.get_text("text", sort=True)
                        text_list.append(page_text)

                        # 限制文本长度
                        if len("\n".join(text_list)) > self.max_text_length:
                            self.logger.warning(f"文本长度超限，截断在第{page_num + 1}页")
                            break

                    text = "\n".join(text_list)

                    # 提取图片（如果启用）
                    if self.extract_images:
                        for page_num, page in enumerate(doc):
                            for img_info in page.get_images(full=True):
                                xref = img_info[0]
                                try:
                                    base_image = doc.extract_image(xref)
                                    if base_image and base_image.get("image"):
                                        images_data.append(base_image["image"])
                                except Exception as img_e:
                                    self.logger.warning(f"无法提取图片 {xref}: {img_e}")

                    # 提取元数据
                    meta = doc.metadata or {}
                    metadata = {
                        'page_count': doc.page_count,
                        'author': meta.get('author'),
                        'publish_date': meta.get('creationDate'),
                        'title': meta.get('title'),
                        'subject': meta.get('subject'),
                        'creator': meta.get('creator'),
                        'producer': meta.get('producer')
                    }

                    self.logger.debug(f"PDF解析完成: 文本长度={len(text)}, 图片数量={len(images_data)}")

            except Exception as e:
                raise DocumentParsingError(doc_path, f"PDF解析失败: {e}")

        return {'text': text, 'images': images_data, 'metadata': metadata}

    @handle_exceptions(reraise=True)
    def _parse_docx(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from a DOCX file."""
        text, images_data, metadata = "", [], {}

        with log_performance(self.logger):
            try:
                doc = docx.Document(doc_path)
                self.logger.debug(f"解析DOCX文件: {doc_path}")

                # 提取文本
                paragraphs = []
                for para in doc.paragraphs:
                    if para.text.strip():
                        paragraphs.append(para.text)
                        # 限制文本长度
                        if len("\n".join(paragraphs)) > self.max_text_length:
                            self.logger.warning("DOCX文本长度超限，已截断")
                            break

                text = "\n".join(paragraphs)

                # 提取图片（如果启用）
                if self.extract_images:
                    for rel in doc.part.rels.values():
                        if "image" in rel.target_ref:
                            try:
                                images_data.append(rel.target_part.blob)
                            except Exception as img_e:
                                self.logger.warning(f"无法提取DOCX图片: {img_e}")

                # 提取元数据
                core_props = doc.core_properties
                metadata = {
                    'page_count': None,  # DOCX页数不可靠
                    'author': core_props.author,
                    'publish_date': str(core_props.created) if core_props.created else None,
                    'title': core_props.title,
                    'subject': core_props.subject,
                    'last_modified_by': core_props.last_modified_by,
                    'modified': str(core_props.modified) if core_props.modified else None
                }

                self.logger.debug(f"DOCX解析完成: 文本长度={len(text)}, 图片数量={len(images_data)}")

            except Exception as e:
                raise DocumentParsingError(doc_path, f"DOCX解析失败: {e}")

        return {'text': text, 'images': images_data, 'metadata': metadata}

    def _parse_txt(self, doc_path: str) -> dict:
        """Reads a TXT file. Estimates page count."""
        text = ""
        try:
            with open(doc_path, 'r', encoding='utf-8', errors='ignore') as f:
                text = f.read()
            # Estimate page count assuming 50 lines per page
            page_count = max(1, len(text.splitlines()) // 50)
            metadata = {'page_count': page_count}
        except Exception as e:
            logging.error(f"Error processing TXT '{doc_path}': {e}")
            metadata = {'page_count': 1}
        return {'text': text, 'images': [], 'metadata': metadata}

    def _parse_epub(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from an EPUB file."""
        text, images_data, metadata = "", [], {}
        try:
            book = epub.read_epub(doc_path)
            content_list = []
            for item in book.get_items():
                item_type = item.get_type()
                if item_type == ITEM_DOCUMENT:
                    soup = BeautifulSoup(item.get_content(), 'html.parser')
                    for script_or_style in soup(["script", "style"]):
                        script_or_style.decompose()
                    content_list.append(soup.get_text("\n", strip=True))
                elif item_type == ITEM_IMAGE:
                    images_data.append(item.get_content())
            text = "\n\n".join(content_list)
            
            meta_dc = book.get_metadata('DC', {})
            author = meta_dc.get('creator', [None])[0]
            pub_date = meta_dc.get('date', [None])[0]
            
            metadata = {'page_count': None, 'author': author, 'publish_date': pub_date}

        except Exception as e:
            logging.error(f"Error processing EPUB '{doc_path}': {e}", exc_info=True)
        return {'text': text, 'images': images_data, 'metadata': metadata}

    def perform_ocr_on_images(self, images_data: list[bytes]) -> str:
        """Performs OCR on a list of image bytes and returns the combined text."""
        self._initialize_ocr()
        if not self.ocr_engine or not images_data:
            return ""
        
        ocr_texts = []
        logging.info(f"Performing OCR on {len(images_data)} image(s)...")
        for i, img_bytes in enumerate(images_data):
            try:
                image = Image.open(io.BytesIO(img_bytes)).convert("RGB")
                img_np = np.array(image)
                result = self.ocr_engine.ocr(img_np, cls=True)
                if result and result[0] is not None:
                    texts = [line[1][0] for line in result[0] if line and len(line) > 1]
                    ocr_texts.extend(texts)
            except Exception as e:
                logging.error(f"OCR failed for image #{i+1}: {e}", exc_info=True)
        
        logging.info("OCR finished.")
        return "\n".join(ocr_texts)

    def parse(self, file_path: str) -> Tuple[str, int]:
        """
        简化的解析方法，返回文本和页数
        为了兼容现有代码
        """
        result = self.parse_document(file_path)
        text = result.get('text', '')
        page_count = result.get('metadata', {}).get('page_count', 0) or 0
        return text, page_count

    @handle_exceptions(reraise=True)
    def parse_document(self, file_path: str) -> dict:
        """
        Parses a document file based on its extension.
        Returns a dictionary containing:
        - 'text': Full text content.
        - 'images': A list of image bytes.
        - 'metadata': A dictionary with author, page_count, etc.
        - 'preamble_text': The first part of the text for AI analysis.
        """
        self.logger.info(f"开始解析文档: {file_path}")

        # 验证文件
        self._validate_file(file_path)

        # 获取文件扩展名
        ext = Path(file_path).suffix.lower()

        # 解析器映射
        parser_map = {
            '.pdf': self._parse_pdf,
            '.docx': self._parse_docx,
            '.txt': self._parse_txt,
            '.epub': self._parse_epub,
        }

        parser_func = parser_map.get(ext)
        if not parser_func:
            raise DocumentParsingError(file_path, f"不支持的文件格式: {ext}")

        # 执行解析
        with log_performance(self.logger):
            parsed_data = parser_func(file_path)

            full_text = parsed_data.get('text', '')

            # 查找目录位置，提取前言部分
            toc_keywords = ["目录", "contents", "table of contents", "索引", "index"]
            lower_text = full_text.lower()
            toc_pos = -1

            for keyword in toc_keywords:
                pos = lower_text.find(keyword, 0, min(50000, len(lower_text)))
                if pos != -1:
                    toc_pos = pos
                    self.logger.debug(f"找到目录关键词 '{keyword}' 在位置 {pos}")
                    break

            # 提取前言文本
            if toc_pos != -1:
                preamble_text = full_text[:toc_pos]
            else:
                preamble_text = full_text[:8000]  # 默认前8000字符

            parsed_data['preamble_text'] = preamble_text
            parsed_data['file_path'] = file_path
            parsed_data['file_size'] = Path(file_path).stat().st_size

            self.logger.info(f"文档解析完成: {file_path}")
            self.logger.debug(f"解析结果: 文本长度={len(full_text)}, 前言长度={len(preamble_text)}")

            return parsed_data