import os
import logging
import sys
import io
import fitz  # PyMuPDF
import docx
from PIL import Image
import numpy as np
from datetime import datetime

# --- Environment Setup for PaddleOCR is now in main.py ---

# Import PaddleOCR after the environment is configured in main.py
from paddleocr import PaddleOCR
from ebooklib import epub, ITEM_DOCUMENT, ITEM_IMAGE
from bs4 import BeautifulSoup

# Suppress noisy logging from dependent libraries
logging.getLogger('ppocr').setLevel(logging.ERROR)
fitz.TOOLS.mupdf_display_errors(False)
fitz.TOOLS.mupdf_display_warnings(False)


class DocumentParser:
    """
    Handles parsing of different document formats, extracting text, images,
    and available metadata.
    """
    def __init__(self):
        self.ocr_engine = None

    def _initialize_ocr(self):
        """Initializes the PaddleOCR engine as a singleton."""
        if self.ocr_engine is None:
            logging.info(f"PADDLE_HOME is set to: {os.environ.get('PADDLE_HOME')}")
            logging.info(f"Initializing PaddleOCR engine. Models will be stored in: {os.environ.get('PADDLE_OCR_MODEL_DIR')}")
            try:
                self.ocr_engine = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
                logging.info("PaddleOCR engine initialized successfully.")
            except Exception as e:
                logging.error(f"Failed to initialize OCR engine: {e}", exc_info=True)
                self.ocr_engine = None # Ensure it's None on failure

    def _parse_pdf(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from a PDF file."""
        text, images_data, metadata = "", [], {}
        try:
            with fitz.open(doc_path) as doc:
                text_list = [page.get_text("text", sort=True) for page in doc]
                text = "\n".join(text_list)
                
                for page in doc:
                    for img_info in page.get_images(full=True):
                        xref = img_info[0]
                        try:
                            base_image = doc.extract_image(xref)
                            if base_image and base_image.get("image"):
                                images_data.append(base_image["image"])
                        except Exception as img_e:
                            logging.warning(f"Could not extract image {xref} from '{doc_path}': {img_e}")
                
                meta = doc.metadata or {}
                metadata = {
                    'page_count': doc.page_count,
                    'author': meta.get('author'),
                    'publish_date': meta.get('creationDate'),
                }
        except Exception as e:
            logging.error(f"Error processing PDF '{doc_path}': {e}")
        return {'text': text, 'images': images_data, 'metadata': metadata}

    def _parse_docx(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from a DOCX file."""
        text, images_data, metadata = "", [], {}
        try:
            doc = docx.Document(doc_path)
            text = "\n".join([para.text for para in doc.paragraphs])
            
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    images_data.append(rel.target_part.blob)
            
            core_props = doc.core_properties
            # Page count is not reliably available in python-docx
            metadata = {
                'page_count': None, 
                'author': core_props.author,
                'publish_date': str(core_props.created) if core_props.created else None,
            }
        except Exception as e:
            logging.error(f"Error processing DOCX '{doc_path}': {e}")
        return {'text': text, 'images': images_data, 'metadata': metadata}

    def _parse_txt(self, doc_path: str) -> dict:
        """Reads a TXT file. Estimates page count."""
        text = ""
        try:
            with open(doc_path, 'r', encoding='utf-8', errors='ignore') as f:
                text = f.read()
            # Estimate page count assuming 50 lines per page
            page_count = max(1, len(text.splitlines()) // 50)
            metadata = {'page_count': page_count}
        except Exception as e:
            logging.error(f"Error processing TXT '{doc_path}': {e}")
            metadata = {'page_count': 1}
        return {'text': text, 'images': [], 'metadata': metadata}

    def _parse_epub(self, doc_path: str) -> dict:
        """Extracts text, images, and metadata from an EPUB file."""
        text, images_data, metadata = "", [], {}
        try:
            book = epub.read_epub(doc_path)
            content_list = []
            for item in book.get_items():
                item_type = item.get_type()
                if item_type == ITEM_DOCUMENT:
                    soup = BeautifulSoup(item.get_content(), 'html.parser')
                    for script_or_style in soup(["script", "style"]):
                        script_or_style.decompose()
                    content_list.append(soup.get_text("\n", strip=True))
                elif item_type == ITEM_IMAGE:
                    images_data.append(item.get_content())
            text = "\n\n".join(content_list)
            
            meta_dc = book.get_metadata('DC', {})
            author = meta_dc.get('creator', [None])[0]
            pub_date = meta_dc.get('date', [None])[0]
            
            metadata = {'page_count': None, 'author': author, 'publish_date': pub_date}

        except Exception as e:
            logging.error(f"Error processing EPUB '{doc_path}': {e}", exc_info=True)
        return {'text': text, 'images': images_data, 'metadata': metadata}

    def perform_ocr_on_images(self, images_data: list[bytes]) -> str:
        """Performs OCR on a list of image bytes and returns the combined text."""
        self._initialize_ocr()
        if not self.ocr_engine or not images_data:
            return ""
        
        ocr_texts = []
        logging.info(f"Performing OCR on {len(images_data)} image(s)...")
        for i, img_bytes in enumerate(images_data):
            try:
                image = Image.open(io.BytesIO(img_bytes)).convert("RGB")
                img_np = np.array(image)
                result = self.ocr_engine.ocr(img_np, cls=True)
                if result and result[0] is not None:
                    texts = [line[1][0] for line in result[0] if line and len(line) > 1]
                    ocr_texts.extend(texts)
            except Exception as e:
                logging.error(f"OCR failed for image #{i+1}: {e}", exc_info=True)
        
        logging.info("OCR finished.")
        return "\n".join(ocr_texts)

    def parse_document(self, file_path: str) -> dict:
        """
        Parses a document file based on its extension.
        Returns a dictionary containing:
        - 'text': Full text content.
        - 'images': A list of image bytes.
        - 'metadata': A dictionary with author, page_count, etc.
        - 'preamble_text': The first part of the text for AI analysis.
        """
        _, ext = os.path.splitext(file_path.lower())
        parser_map = {
            '.pdf': self._parse_pdf,
            '.docx': self._parse_docx,
            '.txt': self._parse_txt,
            '.epub': self._parse_epub,
        }
        
        parser_func = parser_map.get(ext)
        if not parser_func:
            logging.warning(f"Unsupported file type: {ext} for file '{file_path}'")
            return {}
            
        try:
            parsed_data = parser_func(file_path)
            
            full_text = parsed_data.get('text', '')
            
            toc_keywords = ["目录", "contents", "table of contents"]
            lower_text = full_text.lower()
            toc_pos = -1
            for keyword in toc_keywords:
                pos = lower_text.find(keyword, 0, 50000)
                if pos != -1:
                    toc_pos = pos
                    break
            
            parsed_data['preamble_text'] = full_text[:toc_pos] if toc_pos != -1 else full_text[:8000]

            return parsed_data

        except Exception as e:
            logging.error(f"Failed to parse '{file_path}': {e}", exc_info=True)
            return {}