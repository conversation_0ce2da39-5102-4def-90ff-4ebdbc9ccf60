# 文渊阁 - 智能文档分析系统 (优化版)

## 🚀 项目概述

文渊阁是一个基于AI的智能文档分析系统，能够自动解析、分析和管理各种格式的文档。经过全面优化后，系统在性能、稳定性和用户体验方面都有显著提升。

## ✨ 主要特性

### 📄 文档处理
- **多格式支持**: PDF、DOCX、TXT、EPUB等格式
- **智能解析**: 自动提取文本、图片和元数据
- **OCR功能**: 支持图片文字识别
- **并发处理**: 多线程并发处理，提升效率

### 🤖 AI分析
- **智能元数据提取**: 自动识别标题、作者、出版社等信息
- **关键词提取**: 智能提取文档关键词
- **摘要生成**: 自动生成中英文摘要
- **重试机制**: 增强的AI服务稳定性

### 💾 数据管理
- **SQLite数据库**: 高效的本地数据存储
- **缓存系统**: 智能缓存提升查询性能
- **增量更新**: 避免重复处理已分析文档
- **数据备份**: 自动备份重要数据

### 🎨 用户界面
- **现代化UI**: 基于Flet的响应式界面
- **进度显示**: 详细的处理进度和日志
- **设置界面**: 完整的配置管理
- **主题支持**: 支持浅色/深色主题

## 🏗️ 系统架构

```
文渊阁/
├── app/
│   ├── core/                 # 核心模块
│   │   ├── config.py        # 配置管理
│   │   ├── logger.py        # 日志系统
│   │   ├── exceptions.py    # 异常处理
│   │   ├── data_manager.py  # 数据管理
│   │   ├── document_parser.py # 文档解析
│   │   └── project_manager.py # 项目管理
│   ├── models/              # 数据模型
│   │   └── document.py      # 文档模型
│   ├── services/            # 服务层
│   │   └── ollama_client.py # AI服务客户端
│   └── ui/                  # 用户界面
│       ├── main_window.py   # 主窗口
│       ├── settings_dialog.py # 设置对话框
│       └── progress_dialog.py # 进度对话框
├── tests/                   # 测试文件
├── logs/                    # 日志目录
├── data/                    # 数据目录
├── models/                  # AI模型目录
└── config.json             # 配置文件
```

## 🔧 安装和配置

### 环境要求
- Python 3.8+
- Ollama服务 (用于AI分析)

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd wenyuange
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置Ollama**
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull qwen3:4b
```

4. **运行程序**
```bash
python main.py
```

## ⚙️ 配置说明

系统支持通过配置文件或设置界面进行配置：

### AI服务配置
- **服务地址**: Ollama服务的URL
- **模型名称**: 使用的AI模型
- **超时时间**: 请求超时设置
- **温度参数**: AI生成的随机性

### 文档解析配置
- **最大文件大小**: 支持的最大文件大小
- **OCR开关**: 是否启用OCR功能
- **图片提取**: 是否提取文档中的图片

### 界面配置
- **主题模式**: 浅色/深色/跟随系统
- **窗口大小**: 默认窗口尺寸
- **字体设置**: 界面字体配置

### 处理配置
- **并发数量**: 同时处理的文件数
- **重复检测**: 是否启用重复文件检测
- **自动备份**: 分析前是否自动备份

## 🚀 性能优化

### 并发处理
- 支持多线程并发处理文档
- 可配置的并发数量
- 智能任务调度

### 缓存机制
- 数据库查询缓存
- 智能缓存失效
- 内存使用优化

### 错误处理
- 完善的异常处理机制
- 自动重试功能
- 详细的错误日志

## 📊 使用指南

### 基本使用流程

1. **选择目录**: 点击"选择目录"按钮选择要分析的文档目录
2. **开始分析**: 点击"开始分析"按钮启动文档分析
3. **查看进度**: 在进度对话框中查看详细的处理进度
4. **查看结果**: 在不同标签页中查看分析结果

### 高级功能

- **智能搜索**: 在"智能查询"标签中搜索文档
- **重复检测**: 查看"重复文件"标签了解重复文档
- **设置调整**: 点击设置按钮调整系统配置
- **数据管理**: 使用"清除历史"功能管理数据

## 🧪 测试

运行测试套件：
```bash
python tests/test_basic_functionality.py
```

测试覆盖：
- 配置管理测试
- 日志系统测试
- 数据库操作测试
- 文档解析测试
- AI服务测试

## 📝 日志和调试

系统提供完整的日志功能：
- **应用日志**: `logs/wenyuange.log`
- **错误日志**: `logs/wenyuange_error.log`
- **日志级别**: 支持DEBUG、INFO、WARNING、ERROR
- **日志轮转**: 自动管理日志文件大小

## 🔒 安全和隐私

- **本地处理**: 所有数据在本地处理，不上传到云端
- **数据加密**: 敏感数据采用加密存储
- **访问控制**: 严格的文件访问权限控制

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🆘 故障排除

### 常见问题

**Q: Ollama连接失败**
A: 检查Ollama服务是否运行，端口是否正确

**Q: 文档解析失败**
A: 检查文件格式是否支持，文件是否损坏

**Q: 内存使用过高**
A: 减少并发处理数量，清理缓存

**Q: 界面无响应**
A: 检查日志文件，重启应用程序

### 获取帮助

- 查看日志文件获取详细错误信息
- 在GitHub上提交Issue
- 联系开发团队

## 🔄 更新日志

### v2.0.0 (优化版)
- ✅ 重构项目架构
- ✅ 添加配置管理系统
- ✅ 实现并发处理
- ✅ 优化用户界面
- ✅ 增强错误处理
- ✅ 添加缓存机制
- ✅ 完善日志系统
- ✅ 添加单元测试

### v1.0.0 (初始版本)
- ✅ 基础文档解析功能
- ✅ AI分析集成
- ✅ 简单用户界面
