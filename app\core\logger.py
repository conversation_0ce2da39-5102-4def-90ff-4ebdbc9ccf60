"""
统一日志系统
提供结构化的日志记录功能，支持文件和控制台输出
"""
import os
import sys
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional
import traceback


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器（仅在控制台输出时使用）"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class LogManager:
    """日志管理器"""
    
    def __init__(self, 
                 log_dir: str = "logs",
                 app_name: str = "wenyuange",
                 console_level: int = logging.INFO,
                 file_level: int = logging.DEBUG,
                 max_file_size: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 5):
        
        self.log_dir = Path(log_dir)
        self.app_name = app_name
        self.console_level = console_level
        self.file_level = file_level
        self.max_file_size = max_file_size
        self.backup_count = backup_count
        
        # 创建日志目录
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置根日志器
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置日志格式
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_formatter = ColoredFormatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 文件处理器 - 应用日志
        app_log_file = self.log_dir / f"{self.app_name}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(self.file_level)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = self.log_dir / f"{self.app_name}_error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=self.max_file_size,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.console_level)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 记录启动信息
        logger = logging.getLogger(__name__)
        logger.info(f"日志系统已初始化 - 日志目录: {self.log_dir}")
        logger.info(f"文件日志级别: {logging.getLevelName(self.file_level)}")
        logger.info(f"控制台日志级别: {logging.getLevelName(self.console_level)}")
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        return logging.getLogger(name)
    
    def log_exception(self, logger: logging.Logger, message: str = "发生异常"):
        """记录异常信息"""
        logger.error(f"{message}: {traceback.format_exc()}")
    
    def set_level(self, level: int):
        """设置日志级别"""
        logging.getLogger().setLevel(level)
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        try:
            cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
            
            for log_file in self.log_dir.glob("*.log*"):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    logging.getLogger(__name__).info(f"已删除旧日志文件: {log_file}")
                    
        except Exception as e:
            logging.getLogger(__name__).error(f"清理日志文件失败: {e}")


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            self.logger.debug(f"操作耗时: {duration:.2f}秒")


# 全局日志管理器实例
log_manager = LogManager()

# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return log_manager.get_logger(name)

def log_performance(logger: logging.Logger):
    """性能日志装饰器"""
    return PerformanceLogger(logger)
