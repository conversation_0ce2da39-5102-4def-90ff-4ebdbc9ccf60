"""
基础功能测试
测试核心组件的基本功能
"""
import unittest
import tempfile
import os
import sys
from pathlib import Path
import sqlite3

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import ConfigManager
from app.core.logger import LogManager
from app.core.data_manager import DataManager
from app.core.document_parser import DocumentParser
from app.services.ollama_client import OllamaClient
from app.models.document import Document


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
    
    def test_config_creation(self):
        """测试配置创建"""
        config = ConfigManager(self.config_file)
        self.assertIsNotNone(config.ollama)
        self.assertIsNotNone(config.database)
        self.assertIsNotNone(config.parsing)
        self.assertIsNotNone(config.ui)
        self.assertIsNotNone(config.processing)
    
    def test_config_save_load(self):
        """测试配置保存和加载"""
        config = ConfigManager(self.config_file)
        
        # 修改配置
        original_host = config.ollama.host
        config.ollama.host = "http://test:11434"
        config.save_config()
        
        # 重新加载
        config2 = ConfigManager(self.config_file)
        self.assertEqual(config2.ollama.host, "http://test:11434")
        self.assertNotEqual(config2.ollama.host, original_host)


class TestLogManager(unittest.TestCase):
    """日志管理器测试"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def test_log_manager_creation(self):
        """测试日志管理器创建"""
        log_manager = LogManager(log_dir=self.temp_dir)
        self.assertTrue(Path(self.temp_dir).exists())
        
        logger = log_manager.get_logger("test")
        self.assertIsNotNone(logger)
        
        # 测试日志记录
        logger.info("测试信息")
        logger.error("测试错误")
        
        # 检查日志文件是否创建
        log_files = list(Path(self.temp_dir).glob("*.log"))
        self.assertGreater(len(log_files), 0)


class TestDataManager(unittest.TestCase):
    """数据管理器测试"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.db_path = os.path.join(self.temp_dir, "test.db")
        self.data_manager = DataManager(self.db_path)
    
    def test_database_creation(self):
        """测试数据库创建"""
        self.assertTrue(os.path.exists(self.db_path))
        
        # 检查表是否创建
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        self.assertIn("documents", tables)
        self.assertIn("settings", tables)
        self.assertIn("analysis_history", tables)
        
        conn.close()
    
    def test_document_operations(self):
        """测试文档操作"""
        # 创建测试文档
        doc = Document(
            file_hash="test_hash_123",
            doc_name="测试文档",
            doc_type="测试",
            doc_path="/test/path.pdf",
            doc_size_mb=1.5,
            authors="测试作者",
            page_count=10,
            keywords="测试,关键词",
            summary="这是一个测试摘要",
            summary_cn="这是中文摘要"
        )
        
        # 保存文档
        doc_id = self.data_manager.save_document(doc)
        self.assertIsNotNone(doc_id)
        
        # 检查文档是否存在
        exists = self.data_manager.document_exists("test_hash_123")
        self.assertTrue(exists)
        
        # 获取文档
        documents = self.data_manager.get_all_documents()
        self.assertEqual(len(documents), 1)
        self.assertEqual(documents[0].doc_name, "测试文档")
    
    def test_settings_operations(self):
        """测试设置操作"""
        # 保存设置
        self.data_manager.save_setting("test_key", "test_value")
        
        # 获取设置
        value = self.data_manager.get_setting("test_key")
        self.assertEqual(value, "test_value")
        
        # 获取不存在的设置
        value = self.data_manager.get_setting("nonexistent", "default")
        self.assertEqual(value, "default")


class TestDocumentParser(unittest.TestCase):
    """文档解析器测试"""
    
    def setUp(self):
        self.parser = DocumentParser()
        self.temp_dir = tempfile.mkdtemp()
    
    def test_txt_parsing(self):
        """测试TXT文件解析"""
        # 创建测试TXT文件
        txt_file = os.path.join(self.temp_dir, "test.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文档\n包含多行文本\n用于测试解析功能")
        
        # 解析文件
        result = self.parser.parse_document(txt_file)
        
        self.assertIn('text', result)
        self.assertIn('metadata', result)
        self.assertIn('preamble_text', result)
        self.assertIn("测试文档", result['text'])
    
    def test_unsupported_format(self):
        """测试不支持的格式"""
        # 创建不支持的文件
        unsupported_file = os.path.join(self.temp_dir, "test.xyz")
        with open(unsupported_file, 'w') as f:
            f.write("test")
        
        # 应该抛出异常
        with self.assertRaises(Exception):
            self.parser.parse_document(unsupported_file)


class TestOllamaClient(unittest.TestCase):
    """Ollama客户端测试"""
    
    def setUp(self):
        # 注意：这些测试需要Ollama服务运行
        self.client = OllamaClient()
    
    def test_client_creation(self):
        """测试客户端创建"""
        self.assertIsNotNone(self.client)
        self.assertIsNotNone(self.client.model_name)
        self.assertIsNotNone(self.client.api_base)
    
    def test_service_availability(self):
        """测试服务可用性检查"""
        # 这个测试可能会失败，如果Ollama服务未运行
        try:
            available = self.client.is_service_available()
            self.assertIsInstance(available, bool)
        except Exception:
            self.skipTest("Ollama服务不可用")


if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(unittest.makeSuite(TestConfigManager))
    suite.addTest(unittest.makeSuite(TestLogManager))
    suite.addTest(unittest.makeSuite(TestDataManager))
    suite.addTest(unittest.makeSuite(TestDocumentParser))
    suite.addTest(unittest.makeSuite(TestOllamaClient))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        
        for test, traceback in result.failures:
            print(f"\n失败: {test}")
            print(traceback)
            
        for test, traceback in result.errors:
            print(f"\n错误: {test}")
            print(traceback)
