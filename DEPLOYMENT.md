# 文渊阁部署指南

本指南说明了如何将基于 **Flet** 的 "文渊阁" Python 应用程序打包为可在其他 Windows 计算机上运行的独立安装程序。

## 部署流程概述

部署过程分为两个主要阶段：

1.  **打包应用**: 使用 `flet pack` 命令，将所有 Python 代码、Flet UI、依赖项和数据文件（如 OCR 模型）捆绑成一个独立的应用程序目录。
2.  **创建安装程序**: 使用 `Inno Setup` 将生成的应用程序目录和图标包装成一个用户友好的 `setup.exe` 安装向导。

## 部署步骤

### 1. 安装依赖

确保所有必要的库都已安装。最重要的是 `flet`。

```bash
# 安装项目运行所需的所有依赖
pip install -r requirements.txt
```

*请确保 Inno Setup 6 或更高版本已安装并将其添加到了系统 PATH。*

### 2. 下载OCR模型 (关键步骤)

**此步骤与之前相同，至关重要。**
为了让打包后的程序能够在没有网络连接的情况下使用OCR功能，我们需要预先将OCR模型文件下载并包含在安装包中。

**操作:**
-   直接在项目根目录运行主程序一次。
    ```bash
    python main.py 
    # 或者 flet run main.py
    ```
-   程序启动时，会自动检查 `models` 文件夹。如果模型不存在，它会开始下载所需的文件。
-   **重要**: 模型会被下载到项目根目录下的 `models` 文件夹中。这是为了确保 `build.py` 脚本能找到并打包它们。
-   **验证**: 请检查项目根目录，确保 `models` 文件夹及其子文件夹已包含模型文件。

### 3. 执行构建脚本

我们更新了 `build.py` 脚本以使用 `flet pack` 命令。

- **运行构建脚本**:
  打开 PowerShell 或 CMD，然后运行：
  ```bash
  python build.py
  ```
- **脚本功能**:
  - **环境检查**: 自动检查 `main.py`, `wenyuange.ico`, `models` 和 `assets` 文件夹是否存在。
  - **执行打包**: 调用 `flet pack`，并使用正确的参数 (`--name`, `--icon`, `--add-data`) 来捆绑所有必需的文件和目录。
- **构建产物**:
  - 成功后，您将在 `dist/app` 文件夹中找到完整的应用程序。这**不再是单个 .exe 文件**，而是一个包含可执行文件、dll 和其他所有资源的目录。
  - 您可以进入 `dist/app` 目录并双击 `Wenyuange.exe` 进行测试，确保程序能独立运行。

### 4. 使用 Inno Setup 创建安装程序

为了提供更佳的用户体验，我们继续使用 Inno Setup 来创建一个标准的 Windows 安装包。

1.  **安装 Inno Setup**:
    - 如果您尚未安装，请从其官方网站下载并安装 [Inno Setup](https://jrsoftware.org/isinfo.php)。

2.  **检查安装脚本**:
    - `installer.iss` 脚本已被更新，以反映新的目录结构。它现在会打包整个 `dist/app` 目录。

3.  **编译安装程序**:
    - 在 `installer.iss` 文件上右键，选择 "Compile"。
    - 或者，打开 Inno Setup 编译器，通过 "File" -> "Open..." 加载 `installer.iss` 文件，然后点击 "Build" -> "Compile"。
- **最终产物**:
  - 编译成功后，您将在项目根目录下的 `Release` 文件夹中找到最终的安装文件，例如 `Release/Wenyuange-setup.exe`。
  - 这个文件就是您可以分发给最终用户的一键安装包。

---
遵循以上步骤，您就可以成功地为新的 Flet 版 "文渊阁" 创建可分发的安装程序了。 