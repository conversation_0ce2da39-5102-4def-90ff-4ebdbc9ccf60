"""
进度显示对话框组件
提供详细的进度信息和取消功能
"""
import flet as ft
from typing import Optional, Callable
from datetime import datetime
from app.core.logger import get_logger


class ProgressDialog:
    """进度显示对话框"""
    
    def __init__(self, page: ft.Page, title: str = "处理进度", on_cancel: Optional[Callable] = None):
        self.page = page
        self.logger = get_logger(__name__)
        self.title = title
        self.on_cancel = on_cancel
        self.start_time = None
        
        # 创建控件
        self._create_controls()
        
        # 创建对话框
        self.dialog = ft.AlertDialog(
            modal=True,
            title=ft.Text(self.title, size=18, weight=ft.FontWeight.BOLD),
            content=self._create_content(),
            actions=[
                ft.TextButton("取消", on_click=self._on_cancel_click, visible=bool(on_cancel))
            ] if on_cancel else [],
            actions_alignment=ft.MainAxisAlignment.END,
        )
    
    def _create_controls(self):
        """创建控件"""
        # 主进度条
        self.main_progress = ft.ProgressBar(
            width=400,
            height=20,
            bgcolor=ft.Colors.GREY_300,
            color=ft.Colors.BLUE
        )
        
        # 进度文本
        self.progress_text = ft.Text(
            "准备开始...",
            size=14,
            weight=ft.FontWeight.NORMAL
        )
        
        # 详细信息
        self.detail_text = ft.Text(
            "",
            size=12,
            color=ft.Colors.GREY_700
        )

        # 统计信息
        self.stats_text = ft.Text(
            "",
            size=12,
            color=ft.Colors.GREY_600
        )

        # 时间信息
        self.time_text = ft.Text(
            "",
            size=12,
            color=ft.Colors.GREY_600
        )
        
        # 日志列表
        self.log_list = ft.ListView(
            height=200,
            width=400,
            spacing=2,
            auto_scroll=True
        )
        
        # 展开/收起日志按钮
        self.toggle_log_button = ft.TextButton(
            "显示详细日志",
            on_click=self._toggle_log_visibility
        )
        
        self.log_container = ft.Container(
            content=self.log_list,
            visible=False
        )
    
    def _create_content(self):
        """创建对话框内容"""
        return ft.Container(
            content=ft.Column([
                # 进度信息
                self.progress_text,
                self.main_progress,
                ft.Divider(height=1),
                
                # 详细信息
                self.detail_text,
                self.stats_text,
                self.time_text,
                
                # 日志切换按钮
                self.toggle_log_button,
                
                # 日志容器
                self.log_container
            ], tight=True),
            width=450,
            height=300
        )
    
    def _toggle_log_visibility(self, e):
        """切换日志显示"""
        self.log_container.visible = not self.log_container.visible
        self.toggle_log_button.text = "隐藏详细日志" if self.log_container.visible else "显示详细日志"
        
        # 调整对话框高度
        if self.log_container.visible:
            self.dialog.content.height = 550
        else:
            self.dialog.content.height = 300
            
        self.page.update()
    
    def _on_cancel_click(self, e):
        """取消按钮点击"""
        if self.on_cancel:
            self.on_cancel()
    
    def show(self):
        """显示对话框"""
        self.start_time = datetime.now()
        self.page.dialog = self.dialog
        self.dialog.open = True
        self.page.update()
    
    def hide(self):
        """隐藏对话框"""
        self.dialog.open = False
        self.page.update()
    
    def update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        if total > 0:
            progress = current / total
            self.main_progress.value = progress
            
            # 更新进度文本
            percentage = int(progress * 100)
            self.progress_text.value = f"进度: {current}/{total} ({percentage}%)"
            
            # 更新统计信息
            self.stats_text.value = f"已完成: {current}, 总计: {total}, 剩余: {total - current}"
            
            # 更新时间信息
            if self.start_time:
                elapsed = datetime.now() - self.start_time
                elapsed_seconds = elapsed.total_seconds()
                
                if current > 0 and elapsed_seconds > 0:
                    rate = current / elapsed_seconds
                    if rate > 0:
                        remaining_seconds = (total - current) / rate
                        remaining_time = f"{int(remaining_seconds // 60)}分{int(remaining_seconds % 60)}秒"
                    else:
                        remaining_time = "计算中..."
                else:
                    remaining_time = "计算中..."
                
                elapsed_time = f"{int(elapsed_seconds // 60)}分{int(elapsed_seconds % 60)}秒"
                self.time_text.value = f"已用时间: {elapsed_time}, 预计剩余: {remaining_time}"
        
        # 更新详细信息
        if message:
            self.detail_text.value = message
            self.add_log(message)
        
        self.page.update()
    
    def add_log(self, message: str, level: str = "INFO"):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 设置颜色
        color = ft.Colors.BLACK
        if level == "ERROR":
            color = ft.Colors.RED
        elif level == "WARNING":
            color = ft.Colors.ORANGE
        elif level == "SUCCESS":
            color = ft.Colors.GREEN
        
        log_item = ft.Text(
            f"[{timestamp}] {message}",
            size=11,
            color=color
        )
        
        self.log_list.controls.append(log_item)
        
        # 限制日志数量
        if len(self.log_list.controls) > 100:
            self.log_list.controls.pop(0)
        
        if self.log_container.visible:
            self.page.update()
    
    def set_status(self, status: str, level: str = "INFO"):
        """设置状态"""
        self.detail_text.value = status
        self.add_log(status, level)
        self.page.update()
    
    def set_completed(self, message: str = "处理完成"):
        """设置为完成状态"""
        self.main_progress.value = 1.0
        self.progress_text.value = "100% - 完成"
        self.detail_text.value = message
        self.add_log(message, "SUCCESS")
        
        # 隐藏取消按钮
        if self.dialog.actions:
            self.dialog.actions[0].visible = False
        
        # 添加关闭按钮
        self.dialog.actions.append(
            ft.TextButton("关闭", on_click=lambda e: self.hide())
        )
        
        self.page.update()
    
    def set_error(self, error_message: str):
        """设置为错误状态"""
        self.detail_text.value = f"错误: {error_message}"
        self.add_log(f"错误: {error_message}", "ERROR")
        
        # 隐藏取消按钮
        if self.dialog.actions:
            self.dialog.actions[0].visible = False
        
        # 添加关闭按钮
        self.dialog.actions.append(
            ft.TextButton("关闭", on_click=lambda e: self.hide())
        )
        
        self.page.update()
