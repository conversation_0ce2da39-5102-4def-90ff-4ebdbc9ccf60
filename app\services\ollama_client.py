import requests
import json
import time
import threading
from typing import Dict, Optional, List
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from app.core.config import config
from app.core.logger import get_logger
from app.core.exceptions import AIServiceError, handle_exceptions

class OllamaClient:
    """
    用于与Ollama API交互的客户端.
    支持连接池、重试机制、模型自动选择等功能
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, host: str = None):
        """
        初始化Ollama客户端.
        """
        if hasattr(self, '_initialized'):
            return

        self.logger = get_logger(__name__)
        self.host = host or config.ollama.host
        self.api_base = f"{self.host}/api/generate"
        self.api_tags = f"{self.host}/api/tags"
        self.model_name = config.ollama.model_name
        self.timeout = config.ollama.timeout
        self.max_retries = config.ollama.max_retries
        self.retry_delay = config.ollama.retry_delay
        self.temperature = config.ollama.temperature

        # 设置会话和重试策略
        self.session = self._create_session()
        self.available_models = []
        self._check_service_availability()
        self._initialized = True

    def _create_session(self) -> requests.Session:
        """创建带有重试策略的会话"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=self.max_retries,
            backoff_factor=self.retry_delay,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST", "GET"]
        )

        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=20)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session

    def _check_service_availability(self):
        """检查Ollama服务可用性并获取可用模型"""
        try:
            response = self.session.get(self.api_tags, timeout=5)
            response.raise_for_status()

            models_data = response.json()
            self.available_models = [model['name'] for model in models_data.get('models', [])]

            if self.model_name not in self.available_models:
                if self.available_models:
                    # 自动选择可用模型
                    preferred_models = ['qwen3:4b', 'qwen3:8b', 'mistral:latest', 'llama3.1:latest']
                    for preferred in preferred_models:
                        if preferred in self.available_models:
                            self.model_name = preferred
                            self.logger.warning(f"指定模型不可用，自动选择: {self.model_name}")
                            break
                    else:
                        self.model_name = self.available_models[0]
                        self.logger.warning(f"使用第一个可用模型: {self.model_name}")
                else:
                    raise AIServiceError("Ollama", "没有可用的模型")

            self.logger.info(f"Ollama服务可用，当前模型: {self.model_name}")
            self.logger.debug(f"可用模型: {self.available_models}")

        except requests.exceptions.RequestException as e:
            self.logger.error(f"无法连接到Ollama服务: {e}")
            raise AIServiceError("Ollama", f"服务不可用: {e}")

    @handle_exceptions(reraise=True)
    def extract_metadata(self, text: str) -> Dict[str, any]:
        """
        [主功能] 使用Ollama从全文中提取结构化的元数据.
        摘要长度将根据原文长度动态调整.
        """
        # 限制送往模型的文本长度，防止超出上下文限制
        truncated_text = text[:4000]
        
        # 修正：动态计算摘要的目标长度（原文1%，最少50字，最多1000字）
        content_length = len(truncated_text)
        target_summary_length = max(150, min(int(content_length * 0.01), 1000))

        prompt = f"""
Analyze the following document content and return the result strictly in JSON format. Do not add any explanations or markings.

**Core Requirement**: The 'summary' and 'keywords' MUST be in the primary language of the document itself.

Document Content:
---
{truncated_text}
---

Please extract the following fields and return in JSON format:
{{
  "doc_type": "Document type (e.g., Book, Paper, Report, Article, Unknown)",
  "doc_name": "Document name (e.g., book title or paper title, return 'Unknown' if not found)",
  "author": "Author (return 'Unknown' if not found)",
  "publisher": "Publisher or publishing institution (return 'Unknown' if not found)",
  "publish_date": "Publication date (preferably YYYY-MM-DD format). **Analyze carefully**: Look for copyright year (e.g., © 2024), edition dates (e.g., 'May 2024: Second Edition'), or release dates (e.g., '2024-04-26: First Release'). **Always use the latest date found**. If multiple dates are present, the most recent one is the correct publication date. Return 'Unknown' if no date is found.",
  "language": "The main language of the document (e.g., Chinese, English, Russian)",
  "summary": "A detailed and informative summary that covers the core arguments, main content, and conclusions of the article. Minimum length: {target_summary_length} characters. This summary MUST be in the original language of the document.",
  "summary_cn": "A detailed and informative summary in Chinese, with a minimum length of {target_summary_length} characters.",
  "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
}}
"""
        return self._get_structured_response(prompt)

    def extract_from_preamble(self, preamble_text: str) -> Dict[str, any]:
        """
        [辅助功能] 仅从书籍前言（版权页等）中提取核心元数据。
        这是一个专家级的 few-shot prompt.
        """
        prompt = f"""
你是一个图书信息提取专家。你的任务是从书籍的版权页或前言中提取精确的元数据。
请严格按照JSON格式返回，不要添加任何解释。

---
### 示例 1:

**输入文本:**
"Copyright © 2024 by Tom Auger and Emma Saroyan"

**你的输出:**
```json
{{
  "author": "Tom Auger, Emma Saroyan",
  "publisher": "未知",
  "publish_date": "2024"
}}
```

---
### 示例 2:

**输入文本:**
"First edition published 2025
by CRC Press
2385 NW Executive Center Drive, Suite 320, Boca Raton FL 33431
and by CRC Press
4 Park Square, Milton Park, Abingdon, Oxon, OX14 4RN
CRC Press is an imprint of Taylor & Francis Group, LLC
© 2025 Mark Liu"

**你的输出:**
```json
{{
  "author": "Mark Liu",
  "publisher": "CRC Press",
  "publish_date": "2025"
}}
```
---

### 正式任务:

**输入文本:**
"{preamble_text}"

**你的输出:**
"""
        return self._get_structured_response(prompt)

    def analyze_document_general(self, text: str) -> dict:
        """
        使用通用prompt分析文档内容，提取元数据和摘要。
        """
        content_length = len(text)
        # 重新计算摘要长度：1%的内容长度，但限制在[50, 1000]字的区间内
        summary_target_length = max(50, min(int(content_length * 0.01), 1000))

        prompt = f"""
        你是一个专业的文档分析师。请仔细阅读以下文档内容，并严格按照指定的JSON格式返回分析结果。

        **任务要求:**
        1.  **文档名称(name):** 从文本内容中提取出最能代表该文档的正式标题或名称。
        2.  **文档类型(doc_type):** 根据内容和格式，判断该文档的类型（例如："书籍", "论文", "技术报告", "会议纪要", "法律合同", "新闻稿", "研究报告"等）。
        3.  **作者(author):** 提取文档的作者。如果找不到，请返回 "未知"。
        4.  **出版社(publisher):** 提取文档的出版单位或发布机构。如果找不到，请返回 "未知"。
        5.  **出版日期(publish_date):** 提取文档的出版或发布日期。如果找不到，请返回 "未知"。
        6.  **关键词(keywords):** 提取5个最核心的关键词，以JSON数组的形式返回。
        7.  **摘要(summary):** **【重要】** 生成一段详细的摘要，其长度**不得少于 {summary_target_length}字**。摘要需要全面概括文章的核心论点、主要内容和结论。

        **必须严格按照以下JSON格式返回，不要包含任何额外的解释或标记(包括```json和<think>标记):**
        {{
          "name": "提取的文档名称",
          "doc_type": "提取的文档类型",
          "author": "提取的作者",
          "publisher": "提取的出版社",
          "publish_date": "提取的出版日期",
          "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
          "summary": "根据要求生成的详细摘要，其长度至少为{summary_target_length}字。"
        }}

        **文档内容:**
        ---
        {text[:20000]}
        ---
        """
        return self._get_structured_response(prompt)

    def _get_structured_response(self, prompt: str) -> Dict[str, any]:
        """
        发送Prompt并处理返回的JSON的通用方法.
        """
        raw_response = self._get_completion(prompt)
        
        if not raw_response:
            return {}

        try:
            json_start_index = raw_response.find('{')
            if json_start_index == -1:
                raise json.JSONDecodeError("响应中未找到JSON对象。", raw_response, 0)

            json_end_index = raw_response.rfind('}')
            if json_end_index == -1 or json_end_index < json_start_index:
                raise json.JSONDecodeError("响应中未找到完整的JSON对象。", raw_response, 0)
            
            json_string = raw_response[json_start_index : json_end_index + 1]
            return json.loads(json_string)
        except json.JSONDecodeError as e:
            print(f"无法解析Ollama返回的JSON: {e}")
            return { "summary": f"AI响应格式错误: {e}" }

    @handle_exceptions(reraise=True)
    def _get_completion(self, prompt: str, use_json_format: bool = True) -> Optional[str]:
        """
        向Ollama发送请求并获取响应的辅助方法.
        """
        headers = {"Content-Type": "application/json"}
        data = {
            "model": self.model_name,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": self.temperature
            }
        }

        if use_json_format:
            data["format"] = "json"

        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"发送请求到Ollama (尝试 {attempt + 1}/{self.max_retries + 1})")

                response = self.session.post(
                    self.api_base,
                    headers=headers,
                    data=json.dumps(data),
                    timeout=self.timeout
                )
                response.raise_for_status()

                response_data = response.json()
                result = response_data.get("response", "").strip()

                if result:
                    self.logger.debug(f"成功获取响应，长度: {len(result)}")
                    return result
                else:
                    self.logger.warning("响应为空")

            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                raise AIServiceError("Ollama", "请求超时")

            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求失败 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue
                raise AIServiceError("Ollama", f"请求失败: {e}")

            except json.JSONDecodeError as e:
                self.logger.error(f"响应JSON解析失败: {e}")
                raise AIServiceError("Ollama", f"响应格式错误: {e}")

        return None

    def generate(self, prompt: str, use_json_format: bool = False) -> str:
        """生成文本的便捷方法"""
        result = self._get_completion(prompt, use_json_format)
        return result or ""

    def is_service_available(self) -> bool:
        """检查服务是否可用"""
        try:
            response = self.session.get(f"{self.host}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False

    def get_model_info(self) -> Dict[str, any]:
        """获取当前模型信息"""
        try:
            response = self.session.get(f"{self.host}/api/show",
                                      json={"name": self.model_name},
                                      timeout=10)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return {}

    def switch_model(self, model_name: str) -> bool:
        """切换模型"""
        if model_name in self.available_models:
            self.model_name = model_name
            self.logger.info(f"已切换到模型: {model_name}")
            return True
        else:
            self.logger.error(f"模型不可用: {model_name}")
            return False

    def refresh_models(self):
        """刷新可用模型列表"""
        self._check_service_availability()

if __name__ == '__main__':
    print("--- 正在测试Ollama客户端 (增强版) ---")
    
    try:
        requests.get("http://localhost:11434", timeout=3)
    except requests.exceptions.ConnectionError:
        print("错误：无法连接到Ollama服务。")
    else:
        client = OllamaClient()
        sample_text = """
        《Python编程：从入门到实践》
        作者：[美] 埃里克·马瑟斯（Eric Matthes）
        出版社：人民邮电出版社
        出版时间：2020-07-01
        Python是一种高级的、解释性的、通用的编程语言。它的设计哲学强调代码的可读性，
        其语法允许程序员用比C++或Java等语言更少的代码行来表达概念。
        """
        metadata = client.extract_metadata(sample_text)
        
        print("提取到的元数据:")
        print(json.dumps(metadata, indent=2, ensure_ascii=False))
    
    print("---------------------------------") 