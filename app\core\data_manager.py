import os
import sqlite3
import dataclasses
from typing import List, Optional

from app.models.document import Document

class DataManager:
    """Handles all database operations for the application."""

    def __init__(self, db_path: str):
        """Initializes the DataManager and ensures the database table is created."""
        self.db_path = db_path
        # On startup, always reset the database to ensure the schema is correct.
        # This is a development-centric approach.
        if os.path.exists(self.db_path):
             os.remove(self.db_path)
        self._initialize_database()

    def _get_connection(self):
        """Establishes a connection to the SQLite database."""
        # check_same_thread=False is necessary for Flet's multi-threading
        return sqlite3.connect(self.db_path, check_same_thread=False)

    def _initialize_database(self):
        """Creates the necessary tables if they don't exist."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            # This is the single source of truth for the documents table schema.
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_hash TEXT UNIQUE NOT NULL,
                    doc_name TEXT,
                    doc_type TEXT,
                    doc_path TEXT NOT NULL,
                    doc_size_mb REAL,
                    publisher TEXT,
                    publish_date TEXT,
                    authors TEXT,
                    page_count INTEGER,
                    keywords TEXT,
                    summary TEXT,
                    summary_cn TEXT
                )
            ''')
            # This is the single source of truth for the settings table schema.
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            ''')
            conn.commit()

    def save_document(self, doc: Document) -> int:
        """Saves a document (insert or update) to the database."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            doc_dict = dataclasses.asdict(doc)
            doc_dict.pop('id', None) # DB handles the ID
            
            cursor.execute("SELECT id FROM documents WHERE file_hash = ?", (doc.file_hash,))
            result = cursor.fetchone()

            if result: # Update existing document
                doc_id = result[0]
                update_fields = ", ".join([f"{key} = ?" for key in doc_dict])
                values = list(doc_dict.values()) + [doc.file_hash]
                cursor.execute(f"UPDATE documents SET {update_fields} WHERE file_hash = ?", values)
            else: # Insert new document
                columns = ", ".join(doc_dict.keys())
                placeholders = ", ".join(["?"] * len(doc_dict))
                cursor.execute(f"INSERT INTO documents ({columns}) VALUES ({placeholders})", list(doc_dict.values()))
                doc_id = cursor.lastrowid
            
            conn.commit()
            return doc_id if not result else result[0]

    def get_all_documents(self, directory_filter: Optional[str] = None) -> List[Document]:
        """Retrieves documents, correctly filtering by 'doc_path'."""
        with self._get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM documents"
            params = []
            if directory_filter and directory_filter.strip():
                # Correctly use 'doc_path' for filtering.
                query += " WHERE doc_path LIKE ?"
                params.append(f"{os.path.normpath(directory_filter)}%")
            
            query += " ORDER BY id DESC"
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            model_fields = {f.name for f in dataclasses.fields(Document)}
            documents = []
            for row in rows:
                row_data = {key: row[key] for key in row.keys() if key in model_fields}
                documents.append(Document(**row_data))
            return documents

    def document_exists(self, file_hash: str) -> bool:
        """Checks if a document with the given hash exists."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM documents WHERE file_hash = ?", (file_hash,))
            return cursor.fetchone() is not None
    
    def get_setting(self, key: str, default: str = "") -> str:
        """Gets a setting value from the database."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = cursor.fetchone()
            return result[0] if result else default

    def save_setting(self, key: str, value: str):
        """Saves a setting value to the database."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)", (key, value))
            conn.commit()