;
; Wenyuange Inno Setup Script
;

[Setup]
; App details
AppName=文渊阁
AppVersion=1.0
AppPublisher=Wenyuange Project
DefaultDirName={autopf}\Wenyuange
DefaultGroupName=文渊阁
OutputBaseFilename=Wenyuange-setup
Compression=lzma2
SolidCompression=yes
WizardStyle=modern

; Output directory for the installer
OutputDir=Release

; Icon for the installer and uninstaller
SetupIconFile=wenyuange.ico
UninstallDisplayIcon={app}\Wenyuange.exe

[Languages]
Name: "chinesesimp"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; The bundled application directory created by `flet pack`
Source: "dist\app\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; NOTE: All assets (like models, etc.) are now inside the 'dist/app' directory 
; and will be included by the above entry.

[Icons]
Name: "{group}\文渊阁"; Filename: "{app}\Wenyuange.exe"
Name: "{group}\{cm:UninstallProgram,文渊阁}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\文渊阁"; Filename: "{app}\Wenyuange.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\Wenyuange.exe"; Description: "{cm:LaunchProgram,文渊阁}"; Flags: nowait postinstall skipifsilent 