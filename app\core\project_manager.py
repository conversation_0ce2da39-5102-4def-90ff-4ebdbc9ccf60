import os
import sys
import time
import json
import hashlib
from datetime import datetime
from langdetect import detect, LangDetectException
from app.models.document import Document
from app.services.ollama_client import OllamaClient
from app.core.data_manager import DataManager
from app.core.document_parser import DocumentParser
from typing import Dict, Any, List

# --- Helper function for PyInstaller ---
def get_resource_path(relative_path):
    """ Get absolute path to a resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class AnalysisWorker:
    """
    Runs the document analysis in a separate thread, compatible with Flet.
    """
    def __init__(self, selected_dir: str, signals, data_manager: DataManager):
        self.selected_dir = selected_dir
        self.signals = signals
        self.data_manager = data_manager
        self.parser = DocumentParser()
        self.ollama_client = OllamaClient()

    def run(self):
        try:
            self._update_status(f"开始分析目录: {self.selected_dir}", show_progress=True)
            
            files = self._get_files_to_process(self.selected_dir)
            total_files = len(files)
            self._update_status(f"发现 {total_files} 个文件待处理...", show_progress=True)

            for i, file_path in enumerate(files):
                self._update_status(f"正在处理 ({i+1}/{total_files}): {os.path.basename(file_path)}", show_progress=True)
                
                try:
                    file_hash = self._calculate_hash(file_path)
                    if self.data_manager.document_exists(file_hash):
                        self._update_status(f"已跳过(已存在): {os.path.basename(file_path)}", show_progress=True)
                        continue

                    doc = Document(doc_path=file_path, file_hash=file_hash)
                    
                    # 1. Parse content and basic metadata
                    self._update_status(f"({i+1}/{total_files}) 正在解析: {os.path.basename(file_path)}", show_progress=True)
                    parsed_content, page_count = self.parser.parse(file_path)
                    doc.page_count = page_count
                    doc.doc_size_mb = os.path.getsize(file_path) / (1024 * 1024)

                    if not parsed_content or parsed_content.isspace():
                        self._update_status(f"({i+1}/{total_files}) 跳过(无内容): {os.path.basename(file_path)}", show_progress=True)
                        continue

                    # 2. Extract structured data using LLM
                    self._update_status(f"({i+1}/{total_files}) 正在提取元数据: {os.path.basename(file_path)}", show_progress=True)
                    self._extract_metadata(doc, parsed_content)
                    
                    # 3. Extract keywords
                    self._update_status(f"({i+1}/{total_files}) 正在提取关键词: {os.path.basename(file_path)}", show_progress=True)
                    self._extract_keywords(doc, parsed_content)

                    # 4. Generate summary
                    self._update_status(f"({i+1}/{total_files}) 正在生成摘要: {os.path.basename(file_path)}", show_progress=True)
                    self._generate_summary(doc, parsed_content)

                    # 5. Save to database
                    self.data_manager.save_document(doc)
                    self._update_status(f"({i+1}/{total_files}) 已保存: {doc.doc_name}", show_progress=True)

                except Exception as e:
                    self._update_status(f"处理失败 {os.path.basename(file_path)}: {e}", color="red")

            self._update_status("全部分析完成!", show_progress=False)
            self.signals.emit("finished")

        except Exception as e:
            self._update_status(f"分析过程中发生严重错误: {e}", color="red", show_progress=False)
            self.signals.emit("finished")

    def _get_files_to_process(self, selected_dir: str) -> List[str]:
        all_files = []
        for root, _, files in os.walk(selected_dir):
            for file in files:
                if file.lower().endswith(('.pdf', '.docx', '.txt')):
                    all_files.append(os.path.join(root, file))
        return all_files

    def _calculate_hash(self, file_path: str) -> str:
        sha256 = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                while chunk := f.read(8192):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError:
            return ""

    def _extract_metadata(self, doc: Document, content: str):
        # Taking a sample from the beginning of the document for metadata extraction
        sample_content = content[:4000]
        prompt = f"""
        Analyze the beginning of the following document and extract the specified metadata in JSON format.
        The document language can be anything. Your response must be only the JSON object, without any other text or explanations.
        
        Metadata fields to extract:
        - "doc_name": The full, formal title of the document (e.g., "The Art of Programming", "Annual Financial Report 2023").
        - "doc_type": The type of document. Choose from: "Book", "Paper", "Report", "Article", "Letter", "Legal Document", "Other".
        - "authors": A list of author names, as a string separated by commas (e.g., "John Doe, Jane Smith").
        - "publisher": The name of the publisher (e.g., "Springer", "ACM", "Financial Times").
        - "publish_date": The publication date, formatted as YYYY-MM-DD. If not found, leave it as "Unknown".

        Document content sample:
        ---
        {sample_content}
        ---

        Your JSON response:
        """
        response_text = self.ollama_client.generate(prompt)
        try:
            metadata = json.loads(response_text)
            doc.doc_name = metadata.get("doc_name", "未知标题")
            doc.doc_type = metadata.get("doc_type", "未知类型")
            doc.authors = metadata.get("authors", "未知作者")
            doc.publisher = metadata.get("publisher", "未知出版社")
            doc.publish_date = metadata.get("publish_date", "未知日期")
        except json.JSONDecodeError:
            # If LLM fails to return valid JSON, we use the filename as a fallback for the name
            doc.doc_name = os.path.basename(doc.doc_path)

    def _extract_keywords(self, doc: Document, content: str):
        # This is a simple placeholder. A real implementation would use TF-IDF or other NLP techniques.
        # For now, we'll ask the LLM.
        prompt = f"""
        Based on the following text, identify the top 20 most relevant keywords.
        Exclude common stop words. Return the keywords as a single comma-separated string.
        
        Text:
        ---
        {content[:4000]}
        ---

        Keywords:
        """
        keywords_str = self.ollama_client.generate(prompt)
        doc.keywords = keywords_str.strip()

    def _generate_summary(self, doc: Document, content: str):
        content_words = len(content.split())
        summary_word_count = max(50, int(content_words * 0.02)) # At least 50 words, or 2%
        
        summary_prompt = f"""
        Summarize the following document in about {summary_word_count} words.
        The summary should capture the key points and main arguments of the text.
        
        Document:
        ---
        {content}
        ---
        Summary:
        """
        summary = self.ollama_client.generate(summary_prompt)
        doc.summary = summary.strip()

        # Translate to Chinese if the detected language is not Chinese
        # This is a simplified language check.
        if any(c >= '\u4e00' and c <= '\u9fff' for c in content[:1000]):
            doc.summary_cn = doc.summary
        else:
            self._update_status(f"正在翻译摘要: {doc.doc_name}", show_progress=True)
            translation_prompt = f"Translate the following text into Chinese:\n\n{doc.summary}"
            chinese_summary = self.ollama_client.generate(translation_prompt)
            doc.summary_cn = chinese_summary.strip()

    def _update_status(self, message: str, color: str = "black", show_progress: bool = False):
        self.signals.emit("progress", message, color, show_progress)