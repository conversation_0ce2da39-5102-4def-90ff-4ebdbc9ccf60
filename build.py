# build.py
import os
import sys
import subprocess
import shutil

# --- Configuration ---
APP_NAME = "Wenyuange"
MAIN_SCRIPT = "main.py"
ICON_FILE = "wenyuange.ico"
# Directories and files to include in the package
ASSETS_TO_ADD = [
    "models",
    "assets" # Flet's default asset directory
]
DATA_DB_FILE = "data/documents.db"

def print_info(message):
    print(f"✅ [INFO] {message}")

def print_warning(message):
    print(f"⚠️ [WARNING] {message}")

def print_error(message):
    print(f"❌ [ERROR] {message}", file=sys.stderr)
    sys.exit(1)

def check_prerequisites():
    """Verify that necessary files and directories exist."""
    print_info("Checking prerequisites...")
    if not os.path.exists(MAIN_SCRIPT):
        print_error(f"Main script '{MAIN_SCRIPT}' not found.")
    
    if not os.path.exists(ICON_FILE):
        print_error(f"Icon file '{ICON_FILE}' not found. Please run create_icon.py if needed.")

    for asset in ASSETS_TO_ADD:
        if not os.path.exists(asset):
            print_warning(f"Asset directory '{asset}' not found. It will be created, but might be empty.")
            os.makedirs(asset, exist_ok=True)
            
    if not os.path.exists(DATA_DB_FILE):
        print_warning(f"Database file '{DATA_DB_FILE}' not found. The application will create a new one on first run.")

    print_info("Prerequisites check passed.")

def build_executable():
    """Run `flet pack` to build the executable."""
    print_info("Starting Flet build...")
    
    # Flet pack cleans the output directory by default.
    
    command = [
        "flet",
        "pack",
        MAIN_SCRIPT,
        "--name", APP_NAME,
        "--icon", ICON_FILE,
        "--distpath", "dist/app" # Isolate the output
    ]

    for asset in ASSETS_TO_ADD:
        command.extend(["--add-data", f"{asset}:{asset}"])
    
    # Add the database file
    if os.path.exists(DATA_DB_FILE):
        db_dir = os.path.dirname(DATA_DB_FILE)
        command.extend(["--add-data", f"{DATA_DB_FILE}:{db_dir}"])


    print_info(f"Executing command: {' '.join(command)}")
    
    try:
        # Use a simpler subprocess call that streams output
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding='utf-8')
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        rc = process.poll()
        if rc != 0:
            raise subprocess.CalledProcessError(rc, command)

        print_info("Flet build completed successfully.")
        print_info(f"Executable created in: dist\\app")

    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print_error("Flet build failed.")
        print_error(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    check_prerequisites()
    build_executable() 