import dataclasses
from typing import Optional, Dict, Any

@dataclasses.dataclass
class Document:
    """Represents a document with its detailed, extracted metadata."""
    id: Optional[int] = None
    file_hash: str = ""

    # New fields based on the refactoring plan
    doc_name: str = "未知标题"
    doc_type: str = "未知类型"
    doc_path: str = ""
    doc_size_mb: float = 0.0
    publisher: str = "未知出版社"
    publish_date: str = "未知日期"
    authors: str = "未知作者"
    page_count: int = 0
    keywords: str = ""  # Comma-separated string of keywords
    summary: str = ""
    summary_cn: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return dataclasses.asdict(self)