import os
import sys
import time
import json
import hashlib
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue
from typing import Dict, Any, List

from app.models.document import Document
from app.services.ollama_client import OllamaClient
from app.core.data_manager import DataManager
from app.core.document_parser import DocumentParser
from app.core.config import config
from app.core.logger import get_logger, log_performance
from app.core.exceptions import DocumentParsingError, AIServiceError, handle_exceptions

# --- Helper function for PyInstaller ---
def get_resource_path(relative_path):
    """ Get absolute path to a resource, works for dev and for PyInstaller """
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class AnalysisWorker:
    """
    文档分析工作器，支持并发处理和进度报告
    """
    def __init__(self, selected_dir: str, signals, data_manager: DataManager):
        self.selected_dir = selected_dir
        self.signals = signals
        self.data_manager = data_manager
        self.parser = DocumentParser()
        self.ollama_client = OllamaClient()
        self.logger = get_logger(__name__)

        # 并发控制
        self.max_workers = config.processing.max_concurrent_files
        self.stop_event = threading.Event()
        self.processed_count = 0
        self.failed_count = 0
        self.total_files = 0

        # 任务队列
        self.task_queue = Queue()
        self.result_queue = Queue()

    def stop(self):
        """停止分析"""
        self.stop_event.set()
        self.logger.info("分析停止请求已发送")

    @handle_exceptions(reraise=False)
    def run(self):
        """运行分析任务"""
        try:
            self.logger.info(f"开始分析目录: {self.selected_dir}")
            self._update_status(f"开始分析目录: {self.selected_dir}", show_progress=True)

            # 获取文件列表
            files = self._get_files_to_process(self.selected_dir)
            self.total_files = len(files)

            if self.total_files == 0:
                self._update_status("未找到支持的文件", show_progress=False)
                self.signals.emit("finished")
                return

            self._update_status(f"发现 {self.total_files} 个文件待处理...", show_progress=True)

            # 使用线程池并发处理
            if self.max_workers > 1:
                self._run_concurrent(files)
            else:
                self._run_sequential(files)

            # 完成处理
            success_count = self.processed_count
            self._update_status(
                f"分析完成! 成功: {success_count}, 失败: {self.failed_count}, 总计: {self.total_files}",
                show_progress=False
            )
            self.signals.emit("finished")

        except Exception as e:
            self.logger.error(f"分析过程中发生严重错误: {e}")
            self._update_status(f"分析过程中发生严重错误: {e}", color="red", show_progress=False)
            self.signals.emit("finished")

    def _run_sequential(self, files: List[str]):
        """顺序处理文件"""
        for i, file_path in enumerate(files):
            if self.stop_event.is_set():
                break

            self._process_single_file(file_path, i + 1)

    def _run_concurrent(self, files: List[str]):
        """并发处理文件"""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务
            future_to_file = {
                executor.submit(self._process_single_file, file_path, i + 1): file_path
                for i, file_path in enumerate(files)
            }

            # 处理结果
            for future in as_completed(future_to_file):
                if self.stop_event.is_set():
                    break

                file_path = future_to_file[future]
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"处理文件失败 {file_path}: {e}")
                    self.failed_count += 1

    @handle_exceptions(reraise=False)
    def _process_single_file(self, file_path: str, file_index: int):
        """处理单个文件"""
        try:
            filename = os.path.basename(file_path)
            self._update_status(
                f"正在处理 ({file_index}/{self.total_files}): {filename}",
                show_progress=True
            )

            # 检查是否需要停止
            if self.stop_event.is_set():
                return

            # 计算文件哈希
            file_hash = self._calculate_hash(file_path)
            if self.data_manager.document_exists(file_hash):
                self._update_status(f"已跳过(已存在): {filename}", show_progress=True)
                self.processed_count += 1
                return

            # 创建文档对象
            doc = Document(doc_path=file_path, file_hash=file_hash)

            # 1. 解析文档内容
            self._update_status(f"({file_index}/{self.total_files}) 正在解析: {filename}", show_progress=True)
            with log_performance(self.logger):
                parsed_content, page_count = self.parser.parse(file_path)
                doc.page_count = page_count
                doc.doc_size_mb = os.path.getsize(file_path) / (1024 * 1024)

            if not parsed_content or parsed_content.isspace():
                self._update_status(f"({file_index}/{self.total_files}) 跳过(无内容): {filename}", show_progress=True)
                self.processed_count += 1
                return

            # 2. 提取元数据
            if not self.stop_event.is_set():
                self._update_status(f"({file_index}/{self.total_files}) 正在提取元数据: {filename}", show_progress=True)
                self._extract_metadata(doc, parsed_content)

            # 3. 提取关键词
            if not self.stop_event.is_set():
                self._update_status(f"({file_index}/{self.total_files}) 正在提取关键词: {filename}", show_progress=True)
                self._extract_keywords(doc, parsed_content)

            # 4. 生成摘要
            if not self.stop_event.is_set():
                self._update_status(f"({file_index}/{self.total_files}) 正在生成摘要: {filename}", show_progress=True)
                self._generate_summary(doc, parsed_content)

            # 5. 保存到数据库
            if not self.stop_event.is_set():
                self.data_manager.save_document(doc)
                self._update_status(f"({file_index}/{self.total_files}) 已保存: {doc.doc_name}", show_progress=True)
                self.processed_count += 1

                # 通知UI更新
                self.signals.emit("document_found", doc.to_dict())

        except DocumentParsingError as e:
            self.logger.error(f"文档解析失败 {file_path}: {e}")
            self.failed_count += 1
            self._update_status(f"解析失败 {filename}: {e.message}", color="red")

        except AIServiceError as e:
            self.logger.error(f"AI服务错误 {file_path}: {e}")
            self.failed_count += 1
            self._update_status(f"AI处理失败 {filename}: {e.message}", color="red")

        except Exception as e:
            self.logger.error(f"处理文件失败 {file_path}: {e}")
            self.failed_count += 1
            self._update_status(f"处理失败 {filename}: {e}", color="red")

    def _get_files_to_process(self, selected_dir: str) -> List[str]:
        all_files = []
        for root, _, files in os.walk(selected_dir):
            for file in files:
                if file.lower().endswith(('.pdf', '.docx', '.txt')):
                    all_files.append(os.path.join(root, file))
        return all_files

    def _calculate_hash(self, file_path: str) -> str:
        sha256 = hashlib.sha256()
        try:
            with open(file_path, "rb") as f:
                while chunk := f.read(8192):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError:
            return ""

    def _extract_metadata(self, doc: Document, content: str):
        # Taking a sample from the beginning of the document for metadata extraction
        sample_content = content[:4000]
        prompt = f"""
        Analyze the beginning of the following document and extract the specified metadata in JSON format.
        The document language can be anything. Your response must be only the JSON object, without any other text or explanations.
        
        Metadata fields to extract:
        - "doc_name": The full, formal title of the document (e.g., "The Art of Programming", "Annual Financial Report 2023").
        - "doc_type": The type of document. Choose from: "Book", "Paper", "Report", "Article", "Letter", "Legal Document", "Other".
        - "authors": A list of author names, as a string separated by commas (e.g., "John Doe, Jane Smith").
        - "publisher": The name of the publisher (e.g., "Springer", "ACM", "Financial Times").
        - "publish_date": The publication date, formatted as YYYY-MM-DD. If not found, leave it as "Unknown".

        Document content sample:
        ---
        {sample_content}
        ---

        Your JSON response:
        """
        response_text = self.ollama_client.generate(prompt)
        try:
            metadata = json.loads(response_text)
            doc.doc_name = metadata.get("doc_name", "未知标题")
            doc.doc_type = metadata.get("doc_type", "未知类型")
            doc.authors = metadata.get("authors", "未知作者")
            doc.publisher = metadata.get("publisher", "未知出版社")
            doc.publish_date = metadata.get("publish_date", "未知日期")
        except json.JSONDecodeError:
            # If LLM fails to return valid JSON, we use the filename as a fallback for the name
            doc.doc_name = os.path.basename(doc.doc_path)

    def _extract_keywords(self, doc: Document, content: str):
        # This is a simple placeholder. A real implementation would use TF-IDF or other NLP techniques.
        # For now, we'll ask the LLM.
        prompt = f"""
        Based on the following text, identify the top 20 most relevant keywords.
        Exclude common stop words. Return the keywords as a single comma-separated string.
        
        Text:
        ---
        {content[:4000]}
        ---

        Keywords:
        """
        keywords_str = self.ollama_client.generate(prompt)
        doc.keywords = keywords_str.strip()

    def _generate_summary(self, doc: Document, content: str):
        content_words = len(content.split())
        summary_word_count = max(50, int(content_words * 0.02)) # At least 50 words, or 2%
        
        summary_prompt = f"""
        Summarize the following document in about {summary_word_count} words.
        The summary should capture the key points and main arguments of the text.
        
        Document:
        ---
        {content}
        ---
        Summary:
        """
        summary = self.ollama_client.generate(summary_prompt)
        doc.summary = summary.strip()

        # Translate to Chinese if the detected language is not Chinese
        # This is a simplified language check.
        if any(c >= '\u4e00' and c <= '\u9fff' for c in content[:1000]):
            doc.summary_cn = doc.summary
        else:
            self._update_status(f"正在翻译摘要: {doc.doc_name}", show_progress=True)
            translation_prompt = f"Translate the following text into Chinese:\n\n{doc.summary}"
            chinese_summary = self.ollama_client.generate(translation_prompt)
            doc.summary_cn = chinese_summary.strip()

    def _update_status(self, message: str, color: str = "black", show_progress: bool = False):
        self.signals.emit("progress", message, color, show_progress)