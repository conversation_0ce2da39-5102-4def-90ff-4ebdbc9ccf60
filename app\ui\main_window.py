import flet as ft
import os
import subprocess
import threading
from datetime import datetime
from typing import List

from app.core.project_manager import AnalysisWorker
from app.core.data_manager import DataManager
from app.models.document import Document
from app.core.config import config
from app.core.logger import get_logger
from app.core.exceptions import handle_exceptions
from app.ui.settings_dialog import SettingsDialog
from app.ui.progress_dialog import ProgressDialog

class MainWindow:
    def __init__(self, page: ft.Page):
        self.page = page
        self.logger = get_logger(__name__)

        # 应用配置
        self._apply_config()

        # 初始化组件
        self._init_components()

    def _apply_config(self):
        """应用配置到页面"""
        self.page.title = "文渊阁 - 智能文档分析系统"
        self.page.window_width = config.ui.window_width
        self.page.window_height = config.ui.window_height

        # 设置主题
        if config.ui.theme_mode == "dark":
            self.page.theme_mode = ft.ThemeMode.DARK
        elif config.ui.theme_mode == "light":
            self.page.theme_mode = ft.ThemeMode.LIGHT
        else:
            self.page.theme_mode = ft.ThemeMode.SYSTEM

        self.page.vertical_alignment = ft.MainAxisAlignment.START
        self.page.horizontal_alignment = ft.CrossAxisAlignment.STRETCH
        self.page.fonts = {
            "default": config.ui.font_family
        }
        self.page.theme = ft.Theme(
            font_family="default",
            text_theme=ft.TextTheme(
                headline_small=ft.TextStyle(size=12, weight=ft.FontWeight.BOLD),
                title_medium=ft.TextStyle(size=12, weight=ft.FontWeight.BOLD),
                body_medium=ft.TextStyle(size=12),
                body_large=ft.TextStyle(size=12),
            ),
            visual_density=ft.VisualDensity.COMFORTABLE,
            color_scheme_seed="blue"
        )

    def _init_components(self):
        """初始化组件"""
        # --- Data and State ---
        self.data_manager = DataManager()
        self.selected_dir = self.data_manager.get_setting("last_selected_dir", "")
        self.all_documents_cache: List[Document] = []
        self.worker_thread: threading.Thread | None = None
        self.worker: AnalysisWorker | None = None
        self.duplicate_sets = {}

        # --- UI Dialogs ---
        self.settings_dialog = SettingsDialog(self.page, self._on_settings_changed)
        self.progress_dialog = None

        # --- UI Controls ---
        self.file_picker = ft.FilePicker(on_result=self._on_pick_directory_result)
        self.page.overlay.append(self.file_picker)

        self.selected_dir_text = ft.Text(
            f"已选目录: {self.selected_dir}" if self.selected_dir else "尚未选择目录",
            color=ft.Colors.BLACK,
            size=12,
            weight=ft.FontWeight.BOLD,
            expand=True,
            no_wrap=True
        )
        self.select_dir_button = ft.ElevatedButton(
            "选择目录",
            icon=ft.Icons.FOLDER_OPEN,
            on_click=lambda _: self.file_picker.get_directory_path(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=10),
                text_style=ft.TextStyle(size=12, font_family="default"),
            ),
        )
        self.start_analysis_button = ft.ElevatedButton(
            "开始分析",
            icon=ft.Icons.PLAY_ARROW,
            on_click=self._start_analysis,
            disabled=not self.selected_dir,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=10),
                text_style=ft.TextStyle(size=12, font_family="default"),
            ),
        )
        self.cancel_button = ft.ElevatedButton(
            "❌ 取消分析",
            icon=ft.Icons.CANCEL,
            on_click=self.cancel_analysis,
            visible=False,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=10),
                text_style=ft.TextStyle(size=12, font_family="default"),
            ),
        )
        self.clear_history_button = ft.OutlinedButton(
            "🧹 清除历史",
            on_click=self.clear_history,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8),
                padding=ft.padding.symmetric(horizontal=16, vertical=10),
                text_style=ft.TextStyle(size=12, font_family="default"),
            ),
        )

        self.settings_button = ft.IconButton(
            icon=ft.icons.SETTINGS,
            tooltip="设置",
            on_click=lambda _: self.settings_dialog.show(),
            icon_size=20
        )

        self.refresh_button = ft.IconButton(
            icon=ft.icons.REFRESH,
            tooltip="刷新",
            on_click=self._refresh_data,
            icon_size=20
        )

        self.status_label = ft.Text(
            "准备就绪",
            size=12,
            font_family="default",
            color=ft.Colors.BLACK,
            weight=ft.FontWeight.BOLD,
        )
        self.progress_bar = ft.ProgressBar(width=400, visible=False)
        
        # --- Tables ---
        self.all_docs_table = self._create_table()
        self.duplicates_table = self._create_table()
        self.keywords_summary_table = self._create_keywords_summary_table()
        self.query_results_table = self._create_table()

        self.search_field = ft.TextField(
            hint_text="输入关键词进行智能搜索...",
            expand=True,
            border_radius=8,
            on_submit=self.handle_search
        )
        self.search_button = ft.IconButton(
            icon=ft.Icons.SEARCH,
            on_click=self.handle_search,
            tooltip="搜索"
        )

        # --- Layout ---
        self._setup_ui()
        if self.selected_dir:
            self.load_initial_data()

    def _create_table(self) -> ft.DataTable:
        return ft.DataTable(
            columns=[
                ft.DataColumn(ft.Text("文档名称", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("类型", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("作者", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("出版社", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("出版日期", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("页数", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12), numeric=True),
                ft.DataColumn(ft.Text("大小(MB)", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12), numeric=True),
                ft.DataColumn(ft.Text("关键词", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("摘要", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("中文摘要", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
                ft.DataColumn(ft.Text("文件路径", weight=ft.FontWeight.BOLD, color=ft.Colors.BLACK, size=12)),
            ],
            rows=[],
            data_row_max_height=40,
            heading_row_height=40,
            show_checkbox_column=False,
            divider_thickness=1,
            horizontal_margin=8,
            column_spacing=16,
            bgcolor=ft.Colors.WHITE,
        )

    def _create_keywords_summary_table(self) -> ft.DataTable:
        return ft.DataTable(columns=[ft.DataColumn(ft.Text("关键词")), ft.DataColumn(ft.Text("频率"), numeric=True)], rows=[])

    def _setup_ui(self):
        """Builds and lays out the user interface."""

        query_tab_content = ft.Column([
            ft.Row([self.search_field, self.search_button]),
            ft.ResponsiveRow([
                ft.Column([ft.Text("关键词摘要", style=ft.TextThemeStyle.HEADLINE_SMALL), ft.Container(content=self.keywords_summary_table, expand=True, border=ft.border.all(1, ft.Colors.GREY_300), border_radius=ft.border_radius.all(5))], col={"md": 4}),
                ft.Column([ft.Text("查询结果", style=ft.TextThemeStyle.HEADLINE_SMALL), ft.Container(content=ft.Row([self.query_results_table], scroll=ft.ScrollMode.ADAPTIVE, expand=True), expand=True, border=ft.border.all(1, ft.Colors.GREY_300), border_radius=ft.border_radius.all(5))], col={"md": 8}),
            ], expand=True),
        ], expand=True)

        self.tabs = ft.Tabs(
            selected_index=0,
            animation_duration=300,
            tabs=[
                ft.Tab(text="所有文档", content=ft.Row([self.all_docs_table], scroll=ft.ScrollMode.ADAPTIVE, expand=True)),
                ft.Tab(text="重复文件", content=ft.Row([self.duplicates_table], scroll=ft.ScrollMode.ADAPTIVE, expand=True)),
                ft.Tab(text="智能查询", content=query_tab_content),
            ],
            expand=True,
        )
        
        top_controls = ft.Row(
            [
                self.select_dir_button,
                self.selected_dir_text,
                ft.Container(
                    ft.Row([
                        self.start_analysis_button,
                        self.cancel_button,
                        self.clear_history_button,
                    ]),
                    expand=False
                ),
                ft.Container(
                    ft.Row([
                        self.refresh_button,
                        self.settings_button,
                    ]),
                    expand=False
                )
            ],
            alignment=ft.MainAxisAlignment.START,
            spacing=20,
            vertical_alignment=ft.CrossAxisAlignment.CENTER,
            height=40
        )

        status_bar = ft.Row(
            [
                ft.Container(self.status_label, expand=True, padding=ft.padding.only(left=8)),
                self.progress_bar
            ],
            alignment=ft.MainAxisAlignment.START,
            vertical_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=16,
            visible=True, # Always visible
            height=20
        )
        
        # The main layout of the page
        self.page.add(
            ft.Column(
                [
                    top_controls,
                    ft.Divider(height=1, color=ft.Colors.GREY_300),
                    self.tabs, # Tabs are now the main expanding content
                    ft.Divider(height=1, color=ft.Colors.GREY_300),
                    status_bar,
                ],
                expand=True
            )
        )
        self.page.update()

    def _on_pick_directory_result(self, e: ft.FilePickerResultEvent):
        if e.path:
            self.selected_dir = os.path.normpath(e.path)
            self.selected_dir_text.value = f"已选目录: {self.selected_dir}"
            self.data_manager.save_setting("last_selected_dir", self.selected_dir)
            
            # Explicitly enable the button and update its state
            self.start_analysis_button.disabled = False
            self.start_analysis_button.update()
            
            self.load_initial_data()
        else:
            self.selected_dir_text.value = "尚未选择目录"
            self.start_analysis_button.disabled = True
            self.start_analysis_button.update()

        self.selected_dir_text.update()
        # self.page.update() is called within load_initial_data, so this is fine.

    def _on_settings_changed(self):
        """设置更改回调"""
        self.logger.info("设置已更改，重新应用配置")
        self._apply_config()
        self.page.update()

    def _refresh_data(self, e):
        """刷新数据"""
        self.logger.info("刷新数据")
        self.load_initial_data()
        self.status_label.value = "数据已刷新"
        self.status_label.update()

    @handle_exceptions(reraise=False)
    def _start_analysis(self, e):
        if not self.selected_dir or not os.path.isdir(self.selected_dir):
            self.show_alert("请先选择一个有效的目录。")
            return

        # 创建进度对话框
        self.progress_dialog = ProgressDialog(
            self.page,
            title="文档分析进度",
            on_cancel=self.cancel_analysis
        )
        self.progress_dialog.show()

        self.start_analysis_button.visible = False
        self.cancel_button.visible = True
        self.progress_bar.visible = True
        self.status_label.value = "正在准备分析..."
        self.status_label.visible = True
        self.start_analysis_button.update()
        self.cancel_button.update()
        self.progress_bar.update()
        self.status_label.update()

        class Signals:
            def __init__(self, page, main_window):
                self.page = page
                self.main_window = main_window

            def emit(self, signal_name, *args):
                method_name = f"handle_{signal_name}"
                method = getattr(self.main_window, method_name, None)
                if method:
                    # Flet's page.run() is not needed here because event handlers
                    # in Flet already run in the main UI thread context.
                    # We can call the method directly.
                    # The target method itself will handle control updates.
                    try:
                        method(*args)
                    except Exception as e:
                        print(f"Error calling {method_name}: {e}")
        
        signals = Signals(self.page, self)
        
        self.worker = AnalysisWorker(self.selected_dir, signals, self.data_manager)
        self.worker_thread = threading.Thread(target=self.worker.run, daemon=True)
        self.worker_thread.start()

    def cancel_analysis(self, e):
        if self.worker:
            self.worker.stop()
        self.status_label.value = "分析已取消。"
        self.status_label.visible = True
        self.status_label.update()
        self.handle_finished() 

    def _update_tables_from_db(self):
        """Updates the DataTables with the current data from the cache."""
        all_docs = self.all_documents_cache
        self.all_docs_table.rows.clear()
        
        for doc in all_docs:
            row = ft.DataRow(
                cells=[
                    ft.DataCell(ft.Text(doc.doc_name, size=12, color=ft.Colors.BLACK), on_double_tap=lambda e, text=doc.doc_name: self._show_full_text_dialog("文档名称", text)),
                    ft.DataCell(ft.Text(doc.doc_type, size=12, color=ft.Colors.BLACK)),
                    ft.DataCell(ft.Text(doc.authors, size=12, color=ft.Colors.BLACK), on_double_tap=lambda e, text=doc.authors: self._show_full_text_dialog("作者", text)),
                    ft.DataCell(ft.Text(doc.publisher, size=12, color=ft.Colors.BLACK), on_double_tap=lambda e, text=doc.publisher: self._show_full_text_dialog("出版社", text)),
                    ft.DataCell(ft.Text(doc.publish_date, size=12, color=ft.Colors.BLACK)),
                    ft.DataCell(ft.Text(str(doc.page_count), size=12, color=ft.Colors.BLACK)),
                    ft.DataCell(ft.Text(f"{doc.doc_size_mb:.2f}", size=12, color=ft.Colors.BLACK)),
                    ft.DataCell(ft.Text(doc.keywords, size=12, color=ft.Colors.BLACK, overflow=ft.TextOverflow.ELLIPSIS), on_double_tap=lambda e, text=doc.keywords: self._show_full_text_dialog("关键词", text)),
                    ft.DataCell(ft.Text(doc.summary, size=12, color=ft.Colors.BLACK, overflow=ft.TextOverflow.ELLIPSIS, max_lines=1), on_double_tap=lambda e, text=doc.summary: self._show_full_text_dialog("摘要", text)),
                    ft.DataCell(ft.Text(doc.summary_cn, size=12, color=ft.Colors.BLACK, overflow=ft.TextOverflow.ELLIPSIS, max_lines=1), on_double_tap=lambda e, text=doc.summary_cn: self._show_full_text_dialog("中文摘要", text)),
                    ft.DataCell(ft.Text(doc.doc_path, size=12, color=ft.Colors.BLACK, overflow=ft.TextOverflow.ELLIPSIS), on_double_tap=lambda e, path=doc.doc_path: self._open_document(path)),
                ]
            )
            self.all_docs_table.rows.append(row)
        
        self.page.update()

    def load_initial_data(self):
        """Load initial data from DB and update the UI."""
        self.all_documents_cache = self.data_manager.get_all_documents(directory_filter=self.selected_dir)
        self._update_tables_from_db()

    def _open_document(self, doc_path: str):
        """Opens the specified file using the OS default application."""
        try:
            if not os.path.exists(doc_path):
                self._show_full_text_dialog("错误", f"文件不存在:\n{doc_path}")
                return

            if sys.platform == "win32":
                os.startfile(os.path.realpath(doc_path))
            elif sys.platform == "darwin":  # macOS
                subprocess.run(["open", doc_path], check=True)
            else:  # linux
                subprocess.run(["xdg-open", doc_path], check=True)
        except Exception as ex:
            self._show_full_text_dialog("打开文件失败", f"无法打开文件: {doc_path}\n错误: {ex}")

    def handle_started(self, doc_dict: dict):
        self.upsert_document_in_table(self.all_docs_table, doc_dict)
        self.all_docs_table.update()

    def handle_error(self, file_path: str, error_message: str):
        row = self.find_row_by_filepath(self.all_docs_table, file_path)
        if row:
            row.color = ft.Colors.RED_200
            row.update()
        self.handle_progress(f"错误: {file_path} - {error_message}")

    def _populate_row(self, row: ft.DataRow, doc_data: dict):
        file_path = doc_data.get('file_path', '')
        
        def on_open_file(e):
            try:
                if self.page.platform == "windows":
                    os.startfile(file_path)
                elif self.page.platform == "macos":
                    subprocess.call(('open', file_path))
                else: # linux
                    subprocess.call(('xdg-open', file_path))
            except Exception as ex:
                self.show_alert(f"无法打开文件: {ex}")

        def create_cell(content_str, on_double_tap=None, is_long=False):
            return ft.DataCell(
                ft.Text(
                    content_str or "",
                    overflow=ft.TextOverflow.ELLIPSIS,
                    max_lines=2 if is_long else 1,
                    size=12,
                    font_family="default",
                    color=ft.Colors.BLACK,
                ),
                on_double_tap=on_double_tap
            )
        
        def show_full_summary(e, text, title):
            self.show_full_text_dialog(title, text)

        row.cells = [
            create_cell(doc_data.get('filename', '')),
            create_cell(file_path),
            create_cell(f"{doc_data.get('file_size_kb', 0):.2f}"),
            create_cell(str(doc_data.get('publish_date', ''))),
            create_cell(str(doc_data.get('file_modified_date', ''))),
            create_cell(doc_data.get('sha256', '')[:12] + "..."),
            create_cell(
                (doc_data.get('summary', '') or '')[:50] + ("..." if doc_data.get('summary', '') and len(doc_data.get('summary', '')) > 50 else ""),
                on_double_tap=lambda e, s=doc_data.get('summary', ''): show_full_summary(e, s, "完整英文摘要"),
                is_long=True
            ),
            create_cell(
                (doc_data.get('summary_cn', '') or '')[:50] + ("..." if doc_data.get('summary_cn', '') and len(doc_data.get('summary_cn', '')) > 50 else ""),
                on_double_tap=lambda e, s=doc_data.get('summary_cn', ''): show_full_summary(e, s, "完整中文摘要"),
                is_long=True
            ),
            create_cell(
                str(doc_data.get('keywords', []))[:50] + ("..." if str(doc_data.get('keywords', [])) and len(str(doc_data.get('keywords', []))) > 50 else ""),
                on_double_tap=lambda e, s=doc_data.get('keywords', []): show_full_summary(e, str(s), "完整关键词"),
                is_long=True
            ),
        ]

    def upsert_document_in_table(self, table: ft.DataTable, doc_data: dict):
        file_path = doc_data.get('file_path')
        if not file_path:
            return

        existing_row = self.find_row_by_filepath(table, file_path)
        if existing_row:
            self._populate_row(existing_row, doc_data)
        else:
            new_row = ft.DataRow(cells=[])
            self._populate_row(new_row, doc_data)
            table.rows.append(new_row)

    def handle_finished(self):
        self.start_analysis_button.visible = True
        self.cancel_button.visible = False
        self.progress_bar.visible = False
        self.status_label.value = "分析完成。"
        self.status_label.visible = True
        self.update_keyword_summary()
        self.start_analysis_button.update()
        self.cancel_button.update()
        self.progress_bar.update()
        self.status_label.update()
        self.worker = None
        self.worker_thread = None

    def handle_progress(self, message: str, color: str = "black", show_progress: bool = False):
        self.status_label.value = message
        self.status_label.color = color
        self.progress_bar.visible = show_progress
        self.status_label.visible = True
        self.status_label.update()
        self.progress_bar.update()

    def handle_document_found(self, doc_data: dict):
        doc = Document(**doc_data)
        if not any(d.file_path == doc.file_path for d in self.all_documents_cache):
            self.all_documents_cache.append(doc)
        
        self.upsert_document_in_table(self.all_docs_table, doc_data)
        self.all_docs_table.update()

    def handle_duplicate_found(self, duplicate_package: dict):
        original_data = duplicate_package['original']
        duplicate_data = duplicate_package['duplicate']
        
        self.upsert_document_in_table(self.duplicates_table, original_data)
        self.upsert_document_in_table(self.duplicates_table, duplicate_data)

        for doc_data in [original_data, duplicate_data]:
            row = self.find_row_by_filepath(self.all_docs_table, doc_data['file_path'])
            if row:
                row.color = ft.Colors.YELLOW_200
        
        self.all_docs_table.update()
        self.duplicates_table.update()

    def clear_history(self, e):
        def do_clear(e):
            if self.selected_dir:
                self.data_manager.clear_documents_for_directory(self.selected_dir)
            self.load_initial_data()
            close_dialog(e)

        def close_dialog(e):
            self.page.dialog.open = False
            self.page.update()
            
        dlg = ft.AlertDialog(
            modal=True,
            title=ft.Text("确认清除"),
            content=ft.Text("您确定要清除当前目录的所有分析历史记录吗？此操作不可逆。"),
            actions=[
                ft.TextButton("是", on_click=do_clear),
                ft.TextButton("否", on_click=close_dialog),
            ],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        self.page.dialog = dlg
        dlg.open = True
        self.page.update()

    def handle_search(self, e):
        search_term = self.search_field.value.strip().lower()
        if not search_term:
            self.query_results_table.rows.clear()
            self.query_results_table.update()
            return

        self.query_results_table.rows.clear()
        
        results = []
        for doc in self.all_documents_cache:
            keywords_str = ' '.join(doc.keywords).lower() if doc.keywords else ""
            summary = doc.summary.lower() if doc.summary else ""
            if search_term in keywords_str or search_term in summary:
                results.append(doc.to_dict())

        for doc_data in results:
            new_row = ft.DataRow(cells=[])
            self._populate_row(new_row, doc_data)
            self.query_results_table.rows.append(new_row)
        
        self.handle_progress(f"查询到 {len(results)} 个结果。")
        self.query_results_table.update()

    def show_alert(self, message: str):
         def close_dialog(e):
            self.page.dialog.open = False
            self.page.update()
         
         dlg = ft.AlertDialog(title=ft.Text(message), on_dismiss=close_dialog)
         self.page.dialog = dlg
         dlg.open = True
         self.page.update()

    def show_full_text_dialog(self, title, text):
        def close_dialog(e):
            self.page.dialog.open = False
            self.page.update()

        dlg = ft.AlertDialog(
            modal=True,
            title=ft.Text(title),
            content=ft.Column([ft.Text(text or "")], scroll=ft.ScrollMode.AUTO, width=600, height=400),
            actions=[ft.TextButton("关闭", on_click=close_dialog)],
            actions_alignment=ft.MainAxisAlignment.END,
        )
        self.page.dialog = dlg
        dlg.open = True
        self.page.update()

    def update_keyword_summary(self):
        keyword_counts = {}
        for doc in self.all_documents_cache:
            if doc.keywords:
                for kw in doc.keywords:
                    keyword_counts[kw] = keyword_counts.get(kw, 0) + 1
        
        sorted_keywords = sorted(keyword_counts.items(), key=lambda item: item[1], reverse=True)
        
        self.keywords_summary_table.rows.clear()
        for kw, count in sorted_keywords[:50]: # Show top 50
            self.keywords_summary_table.rows.append(
                ft.DataRow(
                    cells=[
                        ft.DataCell(ft.Text(kw)),
                        ft.DataCell(ft.Text(str(count))),
                    ],
                    on_select_changed=lambda e, kw=kw: self.handle_keyword_selection(kw)
                )
            )
        self.keywords_summary_table.update()
        
    def handle_keyword_selection(self, keyword):
        self.search_field.value = keyword
        self.search_field.update()
        self.handle_search(None)

    def _update_status(self, message: str, color: str = ft.Colors.BLACK, show_progress: bool = False):
        self.status_label.value = message
        self.status_label.color = color
        self.progress_bar.visible = show_progress
        self.page.update()

def main_ft(page: ft.Page):
    MainWindow(page)

if __name__ == "__main__":
    ft.app(target=main_ft) 