import os
import sqlite3
import dataclasses
import threading
import time
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from pathlib import Path

from app.models.document import Document
from app.core.config import config
from app.core.logger import get_logger, log_performance
from app.core.exceptions import DatabaseError, handle_exceptions

class DataManager:
    """
    数据库管理器，处理所有数据库操作
    支持连接池、缓存、事务管理等功能
    """

    def __init__(self, db_path: str = None):
        """初始化数据管理器"""
        self.logger = get_logger(__name__)
        self.db_path = db_path or config.database.path
        self._lock = threading.RLock()
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self._last_cache_clear = time.time()

        # 确保数据库目录存在
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)

        # 初始化数据库
        self._initialize_database()

        self.logger.info(f"数据管理器初始化完成: {self.db_path}")

    def _get_connection(self):
        """建立SQLite数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0,
                isolation_level=None  # 自动提交模式
            )
            # 启用外键约束
            conn.execute("PRAGMA foreign_keys = ON")
            # 设置WAL模式以提高并发性能
            conn.execute("PRAGMA journal_mode = WAL")
            # 设置同步模式
            conn.execute("PRAGMA synchronous = NORMAL")
            return conn
        except sqlite3.Error as e:
            raise DatabaseError("connect", f"数据库连接失败: {e}")

    @handle_exceptions(reraise=True)
    def _initialize_database(self):
        """创建必要的数据表和索引"""
        with self._get_connection() as conn:
            cursor = conn.cursor()

            # 创建文档表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    file_hash TEXT UNIQUE NOT NULL,
                    doc_name TEXT,
                    doc_type TEXT,
                    doc_path TEXT NOT NULL,
                    doc_size_mb REAL,
                    publisher TEXT,
                    publish_date TEXT,
                    authors TEXT,
                    page_count INTEGER,
                    keywords TEXT,
                    summary TEXT,
                    summary_cn TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_accessed TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建设置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建分析历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS analysis_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    directory_path TEXT NOT NULL,
                    total_files INTEGER,
                    processed_files INTEGER,
                    failed_files INTEGER,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    status TEXT,
                    error_message TEXT
                )
            ''')

            # 创建索引
            self._create_indexes(cursor)

            conn.commit()
            self.logger.info("数据库初始化完成")

    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_documents_file_hash ON documents(file_hash)",
            "CREATE INDEX IF NOT EXISTS idx_documents_doc_path ON documents(doc_path)",
            "CREATE INDEX IF NOT EXISTS idx_documents_doc_type ON documents(doc_type)",
            "CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_documents_updated_at ON documents(updated_at)",
            "CREATE INDEX IF NOT EXISTS idx_analysis_history_directory ON analysis_history(directory_path)",
            "CREATE INDEX IF NOT EXISTS idx_analysis_history_start_time ON analysis_history(start_time)"
        ]

        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except sqlite3.Error as e:
                self.logger.warning(f"创建索引失败: {e}")

    def _clear_cache_if_needed(self):
        """定期清理缓存"""
        current_time = time.time()
        if current_time - self._last_cache_clear > self._cache_timeout:
            self._cache.clear()
            self._last_cache_clear = current_time
            self.logger.debug("缓存已清理")

    def _get_from_cache(self, key: str) -> Any:
        """从缓存获取数据"""
        self._clear_cache_if_needed()
        return self._cache.get(key)

    def _set_cache(self, key: str, value: Any):
        """设置缓存"""
        self._cache[key] = {
            'data': value,
            'timestamp': time.time()
        }

    def _invalidate_cache(self, pattern: str = None):
        """使缓存失效"""
        if pattern:
            keys_to_remove = [k for k in self._cache.keys() if pattern in k]
            for key in keys_to_remove:
                self._cache.pop(key, None)
        else:
            self._cache.clear()

    @handle_exceptions(reraise=True)
    def save_document(self, doc: Document) -> int:
        """保存文档到数据库（插入或更新）"""
        with self._lock:
            try:
                with self._get_connection() as conn:
                    cursor = conn.cursor()

                    doc_dict = dataclasses.asdict(doc)
                    doc_dict.pop('id', None)  # 数据库处理ID

                    # 添加时间戳
                    current_time = datetime.now().isoformat()
                    doc_dict['updated_at'] = current_time

                    # 检查文档是否已存在
                    cursor.execute("SELECT id FROM documents WHERE file_hash = ?", (doc.file_hash,))
                    result = cursor.fetchone()

                    if result:  # 更新现有文档
                        doc_id = result[0]
                        update_fields = ", ".join([f"{key} = ?" for key in doc_dict])
                        values = list(doc_dict.values()) + [doc.file_hash]
                        cursor.execute(f"UPDATE documents SET {update_fields} WHERE file_hash = ?", values)
                        self.logger.debug(f"更新文档: {doc.doc_name} (ID: {doc_id})")
                    else:  # 插入新文档
                        doc_dict['created_at'] = current_time
                        columns = ", ".join(doc_dict.keys())
                        placeholders = ", ".join(["?"] * len(doc_dict))
                        cursor.execute(f"INSERT INTO documents ({columns}) VALUES ({placeholders})", list(doc_dict.values()))
                        doc_id = cursor.lastrowid
                        self.logger.debug(f"插入新文档: {doc.doc_name} (ID: {doc_id})")

                    conn.commit()

                    # 使相关缓存失效
                    self._invalidate_cache("documents")

                    return doc_id if not result else result[0]

            except sqlite3.Error as e:
                raise DatabaseError("save_document", f"保存文档失败: {e}")

    def get_all_documents(self, directory_filter: Optional[str] = None) -> List[Document]:
        """Retrieves documents, correctly filtering by 'doc_path'."""
        with self._get_connection() as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            query = "SELECT * FROM documents"
            params = []
            if directory_filter and directory_filter.strip():
                # Correctly use 'doc_path' for filtering.
                query += " WHERE doc_path LIKE ?"
                params.append(f"{os.path.normpath(directory_filter)}%")
            
            query += " ORDER BY id DESC"
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            model_fields = {f.name for f in dataclasses.fields(Document)}
            documents = []
            for row in rows:
                row_data = {key: row[key] for key in row.keys() if key in model_fields}
                documents.append(Document(**row_data))
            return documents

    def document_exists(self, file_hash: str) -> bool:
        """Checks if a document with the given hash exists."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM documents WHERE file_hash = ?", (file_hash,))
            return cursor.fetchone() is not None
    
    def get_setting(self, key: str, default: str = "") -> str:
        """Gets a setting value from the database."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = cursor.fetchone()
            return result[0] if result else default

    def save_setting(self, key: str, value: str):
        """Saves a setting value to the database."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)", (key, value))
            conn.commit()

    def clear_documents_for_directory(self, directory_path: str):
        """Clears all documents for a specific directory."""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM documents WHERE doc_path LIKE ?", (f"{os.path.normpath(directory_path)}%",))
            conn.commit()